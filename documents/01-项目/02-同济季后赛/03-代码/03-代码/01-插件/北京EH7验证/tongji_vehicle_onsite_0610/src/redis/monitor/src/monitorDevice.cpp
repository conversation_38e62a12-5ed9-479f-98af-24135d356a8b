#include"monitorDevice.h"
#include <hiredis/hiredis.h>
Json::Value statejson;
monitorDevice::monitorDevice(ros::NodeHandle nh)
{
    std::string node_name = ros::this_node::getName();
    ros::param::get("redis_ip",redis_ip);
    ros::param::get("redis_port",redis_port);    
    ros::param::get("deviceId",m_deviceID);
    ros::param::get("gateway",m_gateway);
    ros::param::get("carID",m_carID);
    ros::param::get("teamName",m_teamName);
    nh.param(node_name+"/serverIP",m_serverIP,m_serverIP);
    nh.param(node_name+"/cpeIP",m_cpeIP,m_cpeIP);
    nh.param(node_name+"/baseIP",m_baseIP,m_baseIP);
    nh.param(node_name+"/net",m_netNum,m_netNum);
    std::string l_pidStr;
    nh.param(node_name+"/alPid",l_pidStr,l_pidStr);
    sub_gps        = nh.subscribe("/sensorgps", 2, &monitorDevice::SubCallback_gps, this);
    sub_actuator   = nh.subscribe("/actuator", 2, &monitorDevice::SubCallback_actuator, this);
    sub_taskStatus = nh.subscribe("/taskstatus", 2, &monitorDevice::SubCallback_taskstatus, this);
    sub_objects    = nh.subscribe("/objectTrack/track_results8CornerForRVIZ", 2, &monitorDevice::SubCallback_object, this);
    getPid(l_pidStr);
    auto l_tmp = std::thread(&monitorDevice::sendRedis, this);
    l_tmp.detach();
   
}

monitorDevice::~monitorDevice()
{

}







void monitorDevice::sendRedis()
{
    ros::Rate rate(10);
    int frameCount = 0;
    // 连接到Redis服务器
#if 1
    redisContext* context = redisConnect(redis_ip.c_str(), redis_port);
    if (context == nullptr || context->err) {
        if (context) {
            std::cerr << "Failed to connect to Redis: " << context->errstr << std::endl;
            return;
        } else {
            std::cerr << "Failed to allocate redis context." << std::endl;
        }
        //return 1;
    }
    redisReply* reply = nullptr;
    if(m_gateway)
    {
        const char* redis_pwd = "Wanji@300552!";
        reply = (redisReply*)redisCommand(context, "AUTH %s", redis_pwd);
        if (reply == nullptr || reply->type == REDIS_REPLY_ERROR) {
            std::cout << "Failed to authenticate with Redis server" << std::endl;
            freeReplyObject(reply);
            redisFree(context);
            // return -1;
        } 
    }
#endif
    long s_lastTime = ros::Time::now().toSec();

    Json::FastWriter l_fastwriter ;
    while (1)
    {

        Json::StreamWriterBuilder writerBuilder;
        Json::Value carData;

        carData["receiveFPS"]= m_topicRecord.simulat;//主车接收仿真帧率
        carData["uploadFPS"]= m_topicRecord.object;//数据上报帧率

        //获取服务状态 0:正常， 1:通信异常    2:定位异常
        bool l_deviceStatus = getGPSStatus();
        if(!l_deviceStatus)
        {
            carData["status"] = 2; //定位异常
        }else
        {
            carData["status"] = 0; //设备正常
        }

        //驾驶状态
        carData["driveStatus"] = m_actuator.sysstatus;//自动驾驶
    
        //获取任务ID
        if(!m_taskStatus.taskStatus)
        {
            carData["taskId"] = 0;
        }else
        {
            std::string l_taskId = getTaskID(m_taskStatus.simResult);
            std::cout<<"taskID:"<<l_taskId<<std::endl;
            carData["taskId"] = atoi(l_taskId.c_str()) ;
        }
        NetworkStats prev_stats = readNetworkStats(m_netNum);
        CpuStat prev_stat = readCpuStat();
        sleep(1);
        NetworkStats curr_stats = readNetworkStats(m_netNum);
         CpuStat curr_stat = readCpuStat();
        double cpu_usage = monitorCPU(prev_stat, curr_stat);
        carData["timestamp"]= ros::Time::now().toSec();
        carData["frameId"]= frameCount;                        // 帧号
        carData["carId"] = m_carID;                            // 车辆ID
        carData["teamName"] = m_teamName;                      // 团队名字
        carData["cpuUsage"] = std::to_string(cpu_usage);       // cpu
        carData["usedMemory"] = monitorMemory();               // 内存
        carData["diskUsage"] = std::to_string(monitorDisk());  // 磁盘

        //获取算法进程状态
        bool l_running = true;
        float l_cpuSum = 0;
        float l_memSum = 0;
        long l_diskSumW = 0;
        long l_diskSumR = 0;
        long l_diskSum = 0;
        for(int i = 0; i < m_pidVec.size(); i++)
        {
            auto l_pid = m_pidVec.at(i);
            l_running &=is_process_running(l_pid);
            //获取cpu占用
            l_cpuSum += getCpuUsage(l_pid);
            //获取内存占用
            l_memSum += getMemoryUsage(l_pid)/1024.0;
            //算法磁盘占用
            getDiskUsage(l_pid, l_diskSumR, l_diskSumW);
            l_diskSum+= l_diskSumW;
        }
        carData["algorithmStatus"] = l_running; //算法状态
        carData["algorithmcpuUsage"] = l_cpuSum; //算法占用cpu的百分比
        carData["algorithmusedMemory"] = l_memSum;  //算法占用内存百分比
        carData["algorithmtotalSize"] = (int)l_diskSum/1000.0; //算法磁盘占用

        std::cout<<carData<<std::endl;
        std::string lineStr  = l_fastwriter.write(carData);
#if 1
        // 发布JSON数据到通道
        std::string l_topic = "TJGKQResult_monitor";//m_taskStatus.gkqResult+"_monitor";//CAR_PROPERTY";// m_taskStatus.fusionResult;
        reply = (redisReply*)redisCommand(context, "PUBLISH %s %s",l_topic.c_str(), lineStr.c_str());
        if (reply == NULL) {
            std::cout << "PUBLISH GKQResult 发布消息失败" << lineStr << std::endl;
        } else {
            std::cout<< std::to_string(ros::Time::now().toSec())<< "   fusions send success "<<l_topic<< "    " << reply->integer << " 个订阅者" << lineStr<<std::endl;
            freeReplyObject(reply);
        }

        if(!m_taskStatus.taskStatus)
        {
            ROS_INFO("task is over");
            return;
        }
     
#endif

        frameCount++;
   
           
           
    }    
}



void monitorDevice::recvRedis()
{
    ROS_INFO("sendDataToCloudTH thread create success!");
    ros::Rate rate(10);
     // 连接到Redis服务器

    struct timeval timeout;
    timeout.tv_sec = 1;         // 1000 milliseconds
    timeout.tv_usec = 0;        // Leave microseconds as 0
    redisContext* context = redisConnectWithTimeout(redis_ip.c_str(), redis_port, timeout);
    if (context == nullptr || context->err) {
        if (context) {
            std::cerr << "Failed to connect to Redis: " << context->errstr << std::endl;
        } else {
            std::cerr << "Failed to allocate redis context." << std::endl;
        }
    }
    redisReply* reply = nullptr;
     if(m_gateway)
     {
        const char* redis_pwd = "Wanji@300552!";
        reply = (redisReply*)redisCommand(context, "AUTH %s", redis_pwd);
        if (reply == nullptr || reply->type == REDIS_REPLY_ERROR) {
            std::cout << "Failed to authenticate with Redis server" << std::endl;
            freeReplyObject(reply);
            redisFree(context);
            // return -1;
        }
     }
    std::string l_cmd = m_taskStatus.simResult;
    ROS_INFO("cloud object SUBSCRIBE channel is %s",l_cmd.c_str());
    reply = (redisReply*)redisCommand(context, "SUBSCRIBE %s",l_cmd.c_str());//TESSResult m_subname
    
    if (reply == nullptr) {
        std::cerr << "Failed to subscribe to HMIControl channel." << std::endl;
        redisFree(context);
        //return 1;
    }
    freeReplyObject(reply);

    Json::FastWriter l_fastwriter ;
    while (ros::ok())
    {
         long l_startTime  = ros::Time::now().toSec()*1000;

        // 开始监听消息
        if (redisGetReply(context, (void**)&reply) == REDIS_OK) {

            if (reply->type == REDIS_REPLY_ARRAY && reply->elements == 3) {
                
                std::string channel = reply->element[1]->str;
                std::string message = reply->element[2]->str;

                // 解析JSON数据
                Json::CharReaderBuilder reader;
                std::string errs;
                std::istringstream iss(message);
    
                if (!Json::parseFromStream(reader, iss, &statejson, &errs)) 
                {
                    std::cerr << "Failed to parse JSON: " << errs << std::endl;
                    continue;
                }
                //保存日志
                m_topicRecord.simulatTime = ros::Time::now().toSec()*1000;
                m_topicRecord.simulat++;

            }
            
            freeReplyObject(reply);
            if(!m_taskStatus.taskStatus)
            {
                ROS_INFO("task is over");
                return;
            }

        }

    }
}



std::string monitorDevice::getTaskID(const std::string p_info)
{
    size_t firstpos = p_info.find('_');
    if(firstpos == std::string::npos)
    {
        return "";
    }
    size_t secondPos = p_info.find('_', firstpos+1);
    if(secondPos == std::string::npos)
    {
        return "";
    }  
    return p_info.substr(firstpos+1, secondPos-firstpos-1);
}


double monitorDevice::pingResust(const std::string &p_ip)
{
    std::stringstream command;
    command << "ping -c 1" << " " << p_ip;

    // 执行命令
    
    FILE* pipe = popen(command.str().c_str(), "r");
    if (!pipe) {
        return -1;
    }

    char buffer[128];
    std::string result = "";
    while(!feof(pipe)) {
        if(fgets(buffer, 128, pipe) != NULL)
            result += buffer;
    }
    pclose(pipe);
    //paser obtain data
    std::istringstream iss(result);
    std::string line;
    double avgLatency = -1;
    bool found = false;
    while(std::getline(iss, line)) {
        if(line.find("rtt min/avg/max/mdev") != std::string::npos) {
            // 提取平均时延
            size_t pos = line.find(" = ");
            if(pos != std::string::npos) {
                std::string latencyPart = line.substr(pos + 3);
                std::istringstream latencyStream(latencyPart);
                double min, avg, max, mdev;
                char slash;
                latencyStream >> min >> slash >> avg >> slash >> max >> slash >> mdev;
                avgLatency = avg;
                found = true;
            }
            break;
        }
    }

    if (!found) {
        std::cerr << "Failed to find average latency in ping output." << std::endl;
    }
    std::cout<<"avgLatency:"<<avgLatency<<std::endl;
    return avgLatency;
}



NetworkStats monitorDevice::readNetworkStats(const std::string& interface)
{
    NetworkStats stats;
    std::ifstream file("/proc/net/dev");
    std::string line;

    while (std::getline(file, line)) {
        if (line.find(interface) != std::string::npos) {
            std::istringstream iss(line);
            std::string iface;
            float l_empty;
            iss >> iface;
            unsigned long long l_value[9];
            for(int k =0; k < 9; k++)
            {
                iss>>l_value[k];
            }
            stats.rx_bytes = l_value[0];
            stats.tx_bytes = l_value[8];
            std::cout<<"iface:"<<iface<<"       "<<stats.rx_bytes<<"      "<<stats.tx_bytes<<std::endl;
            break;
        }
    }

    return stats;
}

void monitorDevice::bandwidthResult()
{
    while (1)
    {
        NetworkStats prev_stats = readNetworkStats(m_netNum);
        sleep(1);  // 等待指定的时间间隔

        NetworkStats curr_stats = readNetworkStats(m_netNum);

        unsigned long long rx_bytes_diff = curr_stats.rx_bytes - prev_stats.rx_bytes;
        unsigned long long tx_bytes_diff = curr_stats.tx_bytes - prev_stats.tx_bytes;
        std::cout << "Download : " << rx_bytes_diff << " bytes" << std::endl;
        std::cout << "Upload : " << tx_bytes_diff << " bytes" << std::endl;
        double interval = 1;
        double rx_bandwidth = static_cast<double>(rx_bytes_diff) / interval / 1024.0 / 1024.0 ;  // KB/s
        double tx_bandwidth = static_cast<double>(tx_bytes_diff) / interval / 1024.0 / 1024.0;  // KB/s
        m_netStats.rx_bytes = rx_bandwidth;
        m_netStats.tx_bytes = tx_bandwidth;

        std::cout << "Download Bandwidth: " << rx_bandwidth << " MB/s  0.1s" << std::endl;
        std::cout << "Upload Bandwidth: " << tx_bandwidth << " MB/s  0.1s"<< std::endl;
    }
}


// 读取CPU统计信息
CpuStat monitorDevice::readCpuStat() {
    CpuStat stat;
    std::ifstream file("/proc/stat");
    std::string line;

    if (file.is_open()) {
        std::getline(file, line);
        std::istringstream iss(line);
        std::string cpu;
        iss >> cpu >> stat.user >> stat.nice >> stat.system >> stat.idle
            >> stat.iowait >> stat.irq >> stat.softirq >> stat.steal
            >> stat.guest >> stat.guest_nice;
    }

    return stat;
}


double  monitorDevice::monitorCPU(CpuStat &prev_stat, CpuStat &curr_stat)
{
    unsigned long long total_time_prev = prev_stat.user + prev_stat.nice + prev_stat.system +
                                         prev_stat.idle + prev_stat.iowait + prev_stat.irq +
                                         prev_stat.softirq + prev_stat.steal + prev_stat.guest +
                                         prev_stat.guest_nice;

    unsigned long long total_time_curr = curr_stat.user + curr_stat.nice + curr_stat.system +
                                         curr_stat.idle + curr_stat.iowait + curr_stat.irq +
                                         curr_stat.softirq + curr_stat.steal + curr_stat.guest +
                                         curr_stat.guest_nice;

    unsigned long long idle_time_prev = prev_stat.idle + prev_stat.iowait;
    unsigned long long idle_time_curr = curr_stat.idle + curr_stat.iowait;

    unsigned long long total_diff = total_time_curr - total_time_prev;
    unsigned long long idle_diff = idle_time_curr - idle_time_prev;

    if (total_diff == 0) {
        return 0.0;
    }

    return 100.0 * (1.0 - (static_cast<double>(idle_diff) / total_diff));
    
}


// 读取内存统计信息
std::unordered_map<std::string, unsigned long long> monitorDevice::readMemInfo() {
    std::ifstream file("/proc/meminfo");
    std::unordered_map<std::string, unsigned long long> mem_info;

    if (file.is_open()) {
        std::string line;
        while (std::getline(file, line)) {
            std::istringstream iss(line);
            std::string key;
            unsigned long long value;
            char unit;  // 用于忽略单位（如kB）

            if (iss >> key >> value >> unit) {
                key.pop_back();  // 去掉冒号
                mem_info[key] = value;
            }
        }
    }

    return mem_info;
}
std::string monitorDevice::monitorMemory()
{
    auto mem_info =  readMemInfo();
    auto it_total = mem_info.find("MemTotal");
    auto it_free = mem_info.find("MemFree");
    auto it_buffers = mem_info.find("Buffers");
    auto it_cached = mem_info.find("Cached");

    if (it_total != mem_info.end() && it_free != mem_info.end() && it_buffers != mem_info.end() && it_cached != mem_info.end()) {
        unsigned long long total_memory = it_total->second;
        unsigned long long free_memory = it_free->second;
        unsigned long long buffers_memory = it_buffers->second;
        unsigned long long cached_memory = it_cached->second;

        unsigned long long used_memory = total_memory - (free_memory + buffers_memory + cached_memory);

        if (total_memory > 0) {
            double lre = (static_cast<double>(used_memory) / total_memory) * 100.0;
            return std::to_string(lre);
        }
    }

    return std::to_string(-1.0);  // 返回-1表示获取失败
}
float monitorDevice::monitorDisk()
{
    std::string path="/";
    struct statvfs stat;
    if (statvfs(path.c_str(), &stat) == 0) {
        unsigned long long total_space = stat.f_blocks * stat.f_frsize;  // 总空间（字节）
        unsigned long long free_space = stat.f_bfree * stat.f_frsize;    // 空闲空间（字节）
        unsigned long long used_space = total_space - free_space;        // 已使用空间（字节）

        if (total_space > 0) {
            return (static_cast<double>(used_space) / total_space) * 100.0;
        }
    }
    return -1.0;  // 返回-1表示获取失败
}




void monitorDevice::SubCallback_gps(const common_msgs::sensorgps::ConstPtr &msg)
{
    m_gps = *msg;
    m_topicRecord.gps++;
    m_topicRecord.gpsTime = ros::Time::now().toSec();
}
void monitorDevice::SubCallback_actuator(const common_msgs::actuator::ConstPtr &msg)
{
    m_actuator = (*msg);
    m_topicRecord.contronCmd++;
    m_topicRecord.controlCmdTime = ros::Time::now().toSec();
}
void monitorDevice::SubCallback_taskstatus(const device_msgs::taskstatus::ConstPtr &msg)
{
    ROS_INFO("recive task status");
    if(m_taskStatus.taskStatus != msg->taskStatus)
    {
        if(!msg->taskStatus)
        {
            m_taskStatus.taskStatus = msg->taskStatus;
        }
        if(msg->taskStatus)
        {
        //    auto l_tmp = std::thread(&monitorDevice::recvRedis, this);
        //   l_tmp.detach();
            auto l_tmp = std::thread(&monitorDevice::sendRedis, this);
            l_tmp.detach();
            m_taskStatus = *msg;
        }       
    }
}
void monitorDevice::SubCallback_object(const common_msgs::sensorgps::ConstPtr &msg)
{
    m_topicRecord.object++;
    m_topicRecord.objectTime = ros::Time::now().toSec();
}



//根据进程ID 对进程状态进行检测
bool monitorDevice::is_process_running(int pid) {
    std::string path = "/proc/" + std::to_string(pid) + "/stat";
    struct stat buffer;
    return (stat(path.c_str(), &buffer) == 0);
}



bool monitorDevice::getGPSStatus()
{
    //第一辆车的异常规则   m_gps.rawstatus

    if(m_gps.rawstatus > 0)
    {
        //定位正常    
        return true;            
    }else
    {
        //定位异常
        return false;
    }


}




// 获取CPU使用率（百分比）
double monitorDevice::getCpuUsage(int pid) {
    static long prevTime = 0;
    static long prevProcessTime = 0;
    
    // 获取系统总时间
    std::ifstream uptime("/proc/uptime");
    long uptimeSec;
    uptime >> uptimeSec;
    uptime.close();
    
    // 获取进程时间
    std::string statPath = "/proc/" + std::to_string(pid) + "/stat";
    std::ifstream stat(statPath);
    std::string line;
    getline(stat, line);
    std::istringstream iss(line);
    std::string ignore;
    long utime, stime;
    for(int i=0; i<13; ++i) iss >> ignore;  // 跳过前13个字段
    iss >> utime >> stime;  // 第14和15字段为CPU时间
    
    long processTime = utime + stime;
    long totalTime = uptimeSec * sysconf(_SC_CLK_TCK);
    
    // 计算百分比
    if(prevTime != 0) {
        double cpuPercent = (processTime - prevProcessTime) * 100.0 
                          / (totalTime - prevTime);
        prevTime = totalTime;
        prevProcessTime = processTime;
        return cpuPercent;
    }
    prevTime = totalTime;
    prevProcessTime = processTime;
    return 0.0;
}

// 获取内存占用（KB）
long monitorDevice::getMemoryUsage(int pid) {
    std::string statusPath = "/proc/" + std::to_string(pid) + "/status";
    std::ifstream status(statusPath);
    std::string line;
    while(getline(status, line)) {
        if(line.find("VmRSS") != std::string::npos) {
            long mem;
            sscanf(line.c_str(), "VmRSS: %ld kB", &mem);
            return mem;
        }
    }
    return 0;
}

// 获取磁盘IO（字节）
void monitorDevice::getDiskUsage(int pid, long& readBytes, long& writeBytes) {
    std::string ioPath = "/proc/" + std::to_string(pid) + "/io";
    std::ifstream io(ioPath);
    std::string line;
    while(getline(io, line)) {
        if(line.find("read_bytes") == 0)
            sscanf(line.c_str(), "read_bytes %ld", &readBytes);
        if(line.find("write_bytes") == 0)
            sscanf(line.c_str(), "write_bytes %ld", &writeBytes);
    }
}



void monitorDevice::getPid(std::string p_string)
{
    std::string token;                      // 临时存储每个子串
    
    // 使用 stringstream 和 getline 分割字符串
    std::istringstream iss(p_string);
    while (getline(iss, token, ',')) { // 注意分隔符是中文逗号
        m_pidVec.push_back(std::stoi(token));
    }
    
    // 打印结果
    
    for (const auto& t : m_pidVec) {
        std::cout << t << std::endl;
    }
}