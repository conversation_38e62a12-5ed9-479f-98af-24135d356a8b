#pragma once

#include <ros/ros.h>
#include <jsoncpp/json/json.h>
#include <json/json.h>
#include "common_msgs/cloudpants.h"
#include "device_msgs/taskpublish.h"
#include "device_msgs/taskstatus.h"
#include "common_msgs/sensorobjects.h"
#include "common_msgs/sensorgps.h"
#include <queue>
#include <mutex>
#include "common.h"
#include <math.h>
#include "wgs84_utm.h"
#include "conversions.h"
#include "json.hpp"

#include <iostream>
#include <fstream>
using json = nlohmann::json;

 
//接收消息至redis
class Redis_sv
{
public:
  Redis_sv(ros::NodeHandle nh);
  ~Redis_sv();
  bool init(void);
  
  bool Setkey(Json::Value jsonData); 

  bool SendcloudpantsWj(Json::Value jsonData); 

  Json::Value Getkey(); 
  Json::Value bulidjson(Json::Value jsonData);

  void SubCallback_wanjigps(const common_msgs::sensorgps::ConstPtr &msg);
  void SubCallback_taskstatus(const device_msgs::taskstatus::ConstPtr &msg);
private:


  void sendObjectToRosTH();
  void printObjects(const json& objects);
  void objectThread();
  void readObjectJson();
  bool SendsensorObjectWj(Json::Value dataArray);
public:
  std::string redis_ip;
  int redis_port;
  std::string m_subname;
  bool m_gateway = true;
  device_msgs::taskstatus m_taskStatus;
private: 
    ros::Publisher pub_cloudpants;
    ros::Subscriber sub_objects;
    ros::Subscriber sub_gps;
    ros::Subscriber sub_wanjigps;
    ros::Subscriber sub_taskStatus;
    ros::Publisher pub_cloudpantsWj;
    ros::Publisher pub_sensorobjWj;
    ros::Publisher pub_stopline;
    std::mutex m_objectQueMutex;

    std::queue<common_msgs::sensorobjects> m_objectQue;

    common_msgs::sensorgps m_wanjigps;
    wgs84_utm m_wgs84_utm;

    common_msgs::sensorobjects m_objects;  
    bool  m_online = false;      
};
