#include "dynamicalModel.h"

char m_zone[] = {0,0,0,0};
dynamicalModel::dynamicalModel(ros::NodeHandle& nh)
{
    std::string str_lon, str_lat;

    ros::param::get("zoneLon",str_lon);
    ros::param::get("zoneLat",str_lat);
    sub_actuator   = nh.subscribe("/controlcmd", 2, &dynamicalModel::SubCallback_controlcmd, this);
    sub_testsub = nh.subscribe("/test_trajectory_req", 2, &dynamicalModel::SubCallback_participantTrajectories, this);
    //cicv_location
    pub_sensorgps = nh.advertise<common_msgs::sensorgps>("/sensorgps", 10);  // wanji to cloud

    double lati = atof(str_lat.c_str());
    double lgti = atof(str_lon.c_str());
    m_start_point.lat = lati;
    m_start_point.lon = lgti;
    m_start_point.x = 0;
    m_start_point.y = 0;
    m_start_point.azimuth = 0;
    m_start_point.speed = 0;
    m_start_point.acc = 0;
    m_start_point.wheel_steer = 0;

    m_current_point.lon = m_start_point.lon;
    m_current_point.lat = m_start_point.lat;
    m_current_point.x = m_start_point.x;
    m_current_point.y = m_start_point.y;
    m_current_point.speed = m_start_point.speed;
    m_current_point.acc = m_start_point.acc;
    m_current_point.azimuth = m_start_point.azimuth;
    m_current_point.wheel_steer = m_start_point.wheel_steer;

    m_wheel_angle_max = 360.0 / m_steer_ratio;              // 前轮转角最大值
    m_wheel_angle_min = -360.0 / m_steer_ratio;             // 前轮转角最小值
    m_delta_wheel_angle_max = 200.0 / m_steer_ratio;        // 每秒最大前轮转角变化量
    m_predict_ts = 1.0 / m_loop;

    auto l_tmpFusion1 = std::thread(&dynamicalModel::rungps, this);
    l_tmpFusion1.detach();

}

dynamicalModel::~dynamicalModel()
{

}

void dynamicalModel::rungps()
{
    while (1)
    {
        if(m_false)
        {
            modelFun(m_contrlmsg);
            usleep((1000.0 / m_loop) * 1000);
        }
        else
        {
            usleep((1000.0 / m_loop) * 1000);
        }
    }
    
}
// 对角度归一化处理
double dynamicalModel::NormalizeAngle(const double angle)
{
    double a = std::fmod(angle, 360.0);
    if (a < 0.0)
    {
        a += 360.0;
    }
    return a;   // [0, 360]
} 

void dynamicalModel::SubCallback_controlcmd(const common_msgs::controlcmd::ConstPtr &msg)
{

  m_contrlmsg = *msg;
//   modelFun(m_contrlmsg);
   
}
void dynamicalModel::SubCallback_participantTrajectories(const device_msgs::participantTrajectories::ConstPtr &msg)
{

    m_false = true;
    std::cout<<"SubCallback_participantTrajectories:"<<msg->value.size()<<std::endl;
    if(msg->value.size() < 0)
    {
        return;
    }
    
    auto l_obj = msg->value.at(0);

    std::cout << "============== start state ===============" << std::endl;
    printf("#### l_obj.courseAngle  = %.5lf\n", l_obj.courseAngle);

    m_lon = l_obj.longitude;
    m_lat = l_obj.latitude;
    m_yaw = l_obj.courseAngle;

    m_start_point.lon = l_obj.longitude;
    m_start_point.lat = l_obj.latitude;
    m_start_point.x = 0;
    m_start_point.y = 0;
    m_start_point.speed = l_obj.speed;
    m_start_point.acc = 0;
    m_start_point.azimuth = NormalizeAngle(l_obj.courseAngle);
    m_start_point.wheel_steer = 0;
    m_current_point.wheel_steer = m_start_point.wheel_steer;

    m_current_point.lon = l_obj.longitude;
    m_current_point.lat = l_obj.latitude;
    m_current_point.x = m_start_point.x;
    m_current_point.y = m_start_point.y;
    m_current_point.speed = m_start_point.speed;
    m_current_point.acc = m_start_point.acc;
    m_current_point.azimuth = m_start_point.azimuth;

    m_sensorgps.heading = NormalizeAngle(l_obj.courseAngle);
    m_sensorgps.lat = m_start_point.lat;
    m_sensorgps.lon = m_start_point.lon;
    m_sensorgps.alt = 0;
    m_sensorgps.speedN = m_start_point.speed * cos(NormalizeAngle(m_sensorgps.heading / 180 * M_PI)) / 3.6;  // km/h -> m/s
    m_sensorgps.speedE = m_start_point.speed * sin(NormalizeAngle(m_sensorgps.heading / 180 * M_PI)) / 3.6;  // km/h -> m/s
    m_sensorgps.speedD = 0;
    m_sensorgps.velocity = m_start_point.speed;
    m_sensorgps.accx = m_start_point.acc;
    m_sensorgps.accy = 0;
    m_sensorgps.accz = 0;   // TODO: 这里似乎应该是g
    m_sensorgps.yawrate = 0;
    m_sensorgps.pitch = 0;
    m_sensorgps.pitchrate = 0;
    m_sensorgps.roll = 0;
    m_sensorgps.rollrate = 0;
    m_sensorgps.gpstime = ros::Time::now().toSec()*1000;
    m_sensorgps.rawstatus = 4;  // TODO: 这里跟全哥确认一下，正常情况下，状态位是4还是5
    m_sensorgps.satenum = 100;  // 卫星数量，随便給
    m_sensorgps.status = 5;     // TODO: 这里跟全哥确认一下，正常情况下，状态位是4还是5
    m_sensorgps.timestamp = ros::Time::now().toSec()*1000;

    pub_sensorgps.publish(m_sensorgps);
    std::cout<<"-----------------------------"<<std::endl;
}


void dynamicalModel::modelFun(common_msgs::controlcmd p_cmd)
{
    gps_common::Point2D l_temp_point = m_current_point; // 当前帧更新运动状态（局部）
    double accel = 0.0;                                 // 输入加速度
    double wheel_angle = 0.0;                           // 输入前轮转角
    
    // 输入限幅
    accel = (p_cmd.acceleration < (m_current_point.acc - m_delta_acc_max * m_predict_ts)) ? 
            (m_current_point.acc - m_delta_acc_max * m_predict_ts) : p_cmd.acceleration;
    accel = (p_cmd.acceleration > (m_current_point.acc + m_delta_acc_max * m_predict_ts)) ? 
            (m_current_point.acc + m_delta_acc_max * m_predict_ts) : p_cmd.acceleration;
    accel = (p_cmd.acceleration < m_acc_min) ? m_acc_min : p_cmd.acceleration;
    accel = (p_cmd.acceleration > m_acc_max) ? m_acc_max : p_cmd.acceleration;
    wheel_angle = (p_cmd.wheel_angle < m_current_point.wheel_steer - m_delta_wheel_angle_max * m_predict_ts) ?
                    m_current_point.wheel_steer - m_delta_wheel_angle_max * m_predict_ts : p_cmd.wheel_angle;
    wheel_angle = (p_cmd.wheel_angle > m_current_point.wheel_steer + m_delta_wheel_angle_max * m_predict_ts) ?
                    m_current_point.wheel_steer - m_delta_wheel_angle_max * m_predict_ts : p_cmd.wheel_angle;
    wheel_angle = (p_cmd.wheel_angle < m_wheel_angle_min) ? m_wheel_angle_min : p_cmd.wheel_angle;
    wheel_angle = (p_cmd.wheel_angle > m_wheel_angle_max) ? m_wheel_angle_max : p_cmd.wheel_angle;
    printf("******* modelFun accel = %lf\n", accel);
    printf("******* modelFun wheel_angle = %lf\n", wheel_angle);

    // 加速度、前轮转角更新
    l_temp_point.acc = accel;
    l_temp_point.wheel_steer = wheel_angle;
    printf("******* l_temp_point.acc = %lf\n", l_temp_point.acc);
    printf("******* l_temp_point.wheel_steer = %lf\n", l_temp_point.wheel_steer);
    // 速度更新
    // v_k+1 = v_k + 0.5 * (a_k + a_k+1) * dt
    l_temp_point.speed = m_current_point.speed + 0.5 * (m_current_point.acc + l_temp_point.acc) * m_predict_ts;
    // 车速限幅
    if (l_temp_point.speed < 0.0)
    {
        l_temp_point.speed = 0;
        // 若限幅后速度差小于输入加速度，对输出加速度限幅
        if ((l_temp_point.speed - m_current_point.speed) / m_predict_ts > l_temp_point.acc)
        {
            l_temp_point.acc = (l_temp_point.speed - m_current_point.speed) / m_predict_ts;
            printf("******* l_temp_point.acc = %lf\n", l_temp_point.acc);
        }
    }
    if (l_temp_point.speed > m_speed_max)
    {
        l_temp_point.speed = m_speed_max;
        // 若限幅后速度差小于输入加速度，对输出加速度限幅
        if (((l_temp_point.speed - m_current_point.speed) / m_predict_ts < l_temp_point.acc))
        {
            l_temp_point.acc = (l_temp_point.speed - m_current_point.speed) / m_predict_ts;
            printf("******* l_temp_point.acc = %lf\n", l_temp_point.acc);
        }
    }
    printf("******* l_temp_point.speed = %lf\n", l_temp_point.speed);
    // 位置更新
    // x_k+1 = x_k + 0.5 * (v_k + v_k+1) * sin(h_k) * dt
    // y_k+1 = y_k + 0.5 * (v_k + v_k+1) * cos(h_k) * dt
    l_temp_point.x  = m_current_point.x + 
                    0.5 * (m_current_point.speed + l_temp_point.speed) * sin(NormalizeAngle(m_current_point.azimuth / 180.0 * M_PI)) * m_predict_ts;
    l_temp_point.y  = m_current_point.y + 
                    0.5 * (m_current_point.speed + l_temp_point.speed) * cos(NormalizeAngle(m_current_point.azimuth / 180.0 * M_PI)) * m_predict_ts;
    printf("******* l_temp_point.x = %lf\n", l_temp_point.x);
    printf("******* l_temp_point.y = %lf\n", l_temp_point.y);
    // 航向角更新
    // h_k+1 = h_k + tan(w_k+1) * v_k * dt / L
    double d_heading = tan(l_temp_point.wheel_steer * M_PI / 180.0) * m_current_point.speed * m_predict_ts / m_wheel_base;  // 当前帧航向角变化量，弧度
    l_temp_point.azimuth = NormalizeAngle(m_current_point.azimuth) + d_heading * 180.0 / M_PI;
    l_temp_point.azimuth = NormalizeAngle(l_temp_point.azimuth);
    printf("******* l_temp_point.azimuth = %lf\n", l_temp_point.azimuth);

    // 转为全局坐标（经纬度）
    XYToLL(m_start_point, l_temp_point);

    // 更新变量 
    m_sensorgps.lon = l_temp_point.lon;
    m_sensorgps.lat = l_temp_point.lat;
    m_sensorgps.alt = 0;
    m_sensorgps.speedN = l_temp_point.speed * cos(NormalizeAngle(l_temp_point.azimuth / 180 * M_PI));
    m_sensorgps.speedE = l_temp_point.speed * sin(NormalizeAngle(l_temp_point.azimuth / 180 * M_PI));
    m_sensorgps.speedD = 0;
    m_sensorgps.velocity = l_temp_point.speed;
    m_sensorgps.accx = l_temp_point.acc;
    m_sensorgps.accy = 0;
    m_sensorgps.accz = 0;   // TODO: 这里似乎应该是g
    m_sensorgps.heading = l_temp_point.azimuth;
    m_sensorgps.yawrate = (l_temp_point.azimuth - m_current_point.azimuth) / m_predict_ts;
    m_sensorgps.pitch = 0;
    m_sensorgps.pitchrate = 0;
    m_sensorgps.roll = 0;
    m_sensorgps.rollrate = 0;
    m_sensorgps.gpstime = ros::Time::now().toSec()*1000;
    m_sensorgps.rawstatus = 4;  // TODO: 这里跟全哥确认一下，正常情况下，状态位是4还是5
    m_sensorgps.satenum = 100;  // 卫星数量，随便給
    m_sensorgps.status = 5;     // TODO: 这里跟全哥确认一下，正常情况下，状态位是4还是5
    m_sensorgps.timestamp = ros::Time::now().toSec()*1000;

    m_current_point = l_temp_point;

    pub_sensorgps.publish(m_sensorgps);

}
