#pragma once
#include <ros/ros.h>
#include <jsoncpp/json/json.h>
#include <vector>
#include <fstream>
#include <iostream>
#include <curl/curl.h>
#include <thread>
#include <hiredis/hiredis.h>
#include <queue>
#include <openssl/md5.h>
#include <iomanip>
#include <filesystem>
#include <string>
#include "yaml-cpp/yaml.h"
struct imageIDInfo_t
{
    std::string p_cloudId;
    std::string p_localID;
};

class imageInfo
{
private:
    std::string m_ip;
    int m_port;
    bool m_gateway;
    std::string m_deviceId;
    std::queue<imageIDInfo_t> m_imageQue;
    std::string m_fileName;
    std::string m_shellFile;
public:
    imageInfo(std::string p_ip, int p_port, bool p_gateWay);
    ~imageInfo();
    void setImageID(std::string ID);
    Json::Value parseImageListToCloud();
    void pullImageMonitor(Json::Value p_json);
    bool createContainer(std::string p_name);
    bool deleteContainer(std::string p_name);
    void deleteImage(Json::Value p_json);
    std::string findNewLocalImageID();
    void saveImage(std::string p_imageID, std::string p_localImageID);
    std::string findlocalImageID(const std::string p_cloudImageID);
    bool loadImage(std::string p_name, std::string p_imageCloudID);
    bool findImageDir( std::string p_imageID);
    bool openContainer(std::string p_name);
    bool stopContainer(std::string p_name);
    bool stopContainer();
    void deleteImageFileTar(Json::Value p_json);
private:
    std::queue<imageIDInfo_t>  reloadImageJson();
    bool findImageID(const std::string p_cloudImageID);
    std::string calculateFileMD5(const std::string& p_filePath);
    
    void containerRun(std::string p_name, std::string p_imageID);
   
    int pullImage(std::string p_name, std::string p_imageID, std::string &p_path);
    bool sendToRedis(Json::Value p_json, std::string p_channel);
    std::vector<std::string> getImagesIDList(); 
    std::vector<std::string> getCmdList(std::string p_cmd, int p_index);
    std::string exeCmd(std::string p_info);
    // find imageID and protoImageID
    std::queue<imageIDInfo_t> readJson(const std::string p_fileName);
    void writeJson(const std::string p_fileName,std::queue<imageIDInfo_t> p_image);
    
    void deleteImage(std::string p_cloudImageID);
    
    // delete imageID from protoImage;

};

