#include "redisInfo.h"
#include "commonkey.h"
redisInfo::redisInfo(ros::NodeHandle nh)
{
    std::string node_name = ros::this_node::getName();

    // 验证授权
    std::string l_activationCode;
    ros::param::get("activationCode",l_activationCode);
    bool l_re= getResult(l_activationCode);
    if(!l_re)
    {
        exit(-1);
    }

    ros::param::get("redis_ip",redis_ip);
    ros::param::get("redis_port",redis_port);
    ros::param::get("gateway",m_gateway);
    ros::param::get("deviceId",m_deviceIdNum);
    std::cout<<"m_deviceIdNum:"<<m_deviceIdNum<<std::endl;
    nh.param(node_name+"/taskTopic",m_taskTopic,m_taskTopic);
    m_deviceId = "YK001";
    m_trajectory = new trajectoryInfo(redis_ip, redis_port,m_gateway);

    //发送仿真轨迹
    pub_mainTrajectory = nh.advertise<device_msgs::simulationPublish>("/simulation_trajectory", 10);
    //发送练习
    pub_simulationTrajectory = nh.advertise<device_msgs::participantTrajectories>("/test_trajectory_req", 10);

    //发送其它节点的任务状态和信息
    pub_taskStatus = nh.advertise<device_msgs::taskstatus>("/taskstatus", 10);


    sub_taskResult = nh.subscribe("/test_trajectory_result", 1000, &redisInfo::SubCallback_taskResult, this);   //ros订阅端
    //发送开始数据
    pub_startCompetition = nh.advertise<device_msgs::taskpublish>("/start_task", 10);

    pub_pubTrajToDe = nh.advertise<common_msgs::hdroute>("/hd_route_points", 10);
    sub_actuator   = nh.subscribe("/controllon", 1000, &redisInfo::SubCallback_actuator, this);
    //创建监控云端消息的线程
    auto l_runTH = std::thread(&redisInfo::run, this);
	l_runTH.detach();
    auto l_monitorTH = std::thread(&redisInfo::monitorCloudData, this);
	l_monitorTH.detach();
    m_actuatorTime = ros::Time::now().toSec()*1000;

}

redisInfo::~redisInfo()
{
    delete m_trajectory;

}   

//接收选手的任务结果
void redisInfo::SubCallback_taskResult(const device_msgs::tessResult::ConstPtr &msg)
{
    // m_trajectory->taskResultToCloud(msg->status);
}


void redisInfo::SubCallback_actuator(const common_msgs::controllon::ConstPtr &msg)
{

    std::unique_lock<std::mutex> lck(m_actuatorFlagMutex);
    m_actuatorTime = ros::Time::now().toSec()*1000;
    lck.unlock();
}
Json::Value statejson;
void redisInfo::run()
{
    ros::Rate rate(10);

     redisContext* context = redisConnect(redis_ip.c_str(), redis_port);
    if (context == nullptr || context->err) {
        if (context) {
            std::cerr << "Failed to connect to Redis: " << context->errstr << std::endl;
        } else {
            std::cerr << "Failed to allocate redis context." << std::endl;
        }
        //return 1;
    }

    redisReply* reply = nullptr;
     if(m_gateway)
     {
        const char* redis_pwd = "Wanji@300552!";
        reply = (redisReply*)redisCommand(context, "AUTH %s", redis_pwd);
        if (reply == nullptr || reply->type == REDIS_REPLY_ERROR) {
            std::cout << "Failed to authenticate with Redis server" << std::endl;
            freeReplyObject(reply);
            redisFree(context);
            // return -1;
        }
     }


    std::string channel5 = m_taskTopic;

    reply = (redisReply*)redisCommand(context, "SUBSCRIBE %s",channel5.c_str());//TESSResult m_subname
    if (reply == nullptr) {
        std::cerr << "Failed to subscribe to HMIControl channel." << std::endl;
        redisFree(context);
        //return 1;
    }




    freeReplyObject(reply);

#if 1
    while (ros::ok())
    {
        ros::spinOnce();
 
        // 开始监听消息
        while (redisGetReply(context, (void**)&reply) == REDIS_OK) {
            // std::cout<<"reply->elements:"<<(int)reply->elements<<std::endl;
           ros::spinOnce();
            if (reply->type == REDIS_REPLY_ARRAY && reply->elements == 3) {
                std::string channel = reply->element[1]->str;
                std::string message = reply->element[2]->str;
                std::cout<<"channel:"<<channel<<std::endl;

                std::vector<std::string> l_temp;
                l_temp.push_back(channel);
                l_temp.push_back(message);
                std::unique_lock<std::mutex> lck(m_cloudMsgMutex);
                m_cloudMsgQue.push(l_temp);
                lck.unlock();

            }
            
            freeReplyObject(reply);
        }
        {
            std::cout<<"REDIS is not ok"<<std::endl;
        }

      
        
        rate.sleep();
    }
#endif
}


void redisInfo::monitorCloudData()
{
    deviceStatus(m_deviceIdNum);
    while (1)
    {
        std::unique_lock<std::mutex> lck(m_cloudMsgMutex);
        if(!m_cloudMsgQue.empty())
        {
            auto l_queue  = m_cloudMsgQue;
            while (!m_cloudMsgQue.empty())
            {
                m_cloudMsgQue.pop();
            }
            
            lck.unlock();
            while (!l_queue.empty())
            {
                auto l_data = l_queue.front();
                l_queue.pop();
                std::string l_channel = l_data.at(0);
                std::string    l_msg  = l_data.at(1);
                // std::cout<<"l_msg:"<<l_msg<<std::endl;  
                // 解析JSON数据
                Json::CharReaderBuilder reader;
                std::string errs;
                std::istringstream iss(l_msg);
                std::cout<<"============================================"<<std::endl;
                std::cout<<"recv cloud data:"<<l_msg<<std::endl;
                std::cout<<"============================================"<<std::endl;
                if (!Json::parseFromStream(reader, iss, &statejson, &errs)) 
                {}
                //查看设备ID是否存在
                int l_re = statejson["type"].asInt();
                if(l_re == 1)   // 协议 3.1 平台查看设备在线，下发关键点
                {
       
                    std::cout<<"testScene:"<<statejson<<std::endl;
                    m_Trajectories = m_trajectory->testScene(statejson, pub_simulationTrajectory);
                    std::unique_lock<std::mutex> lck(m_firstTrajectotyMutex);
                    m_firstTrajectoty = true;
                    lck.unlock();   
                }else if(l_re == 0) // 协议 2.1 设备是否在线
                {
                    //std::unique_lock<std::mutex> lck(m_firstTrajectotyMutex);
                    //m_firstTrajectoty = true;
                    //lck.unlock();
                }else if(l_re == 2)   // 协议4.1 测试任务开始
                {
                    pubTaskStatus(statejson);
                }                  
            }
            
        }else
        {
            lck.unlock();
        }
        
        usleep(10*100);
    }
    
}


//解析域控规控算法交互的参数下发
void redisInfo::pubTaskStatus(Json::Value p_json)
{
    //创建话题  发给其它节点，发送车辆信息
    ROS_INFO("task have create");
    uint8_t l_tmp = p_json["type"].asInt();
    std::cout<<"p_json:"<<p_json<<std::endl;
    Json::Value dataArray =  p_json["params"]["protocols"];
    std::string taskID;
    std::string caseID;
    std::string av_str;
    std::string l_msgTypeZero;
    std::string l_msgTypeOne;
    for(const auto& item:dataArray)
    {
        int l_type = item["type"].asInt();
        if(l_type == 0)
        {
            l_msgTypeZero = item["channel"].asString();  //

        }else if(l_type == 1)
        {
            l_msgTypeOne = item["params"]["mergeDataChannel"].asString();   //仿真轨迹下发
            av_str = item["channel"].asString();
            taskID = item["params"]["taskId"].asString();  // caseId
            caseID = item["params"]["caseId"].asString();  // caseId
        }
        std::cout<<"=======================:"<<m_msgTypeTwo<<"          "<<item["params"]["taskId"].asString()<<std::endl;
    }
    
    device_msgs::taskstatus l_task;
    l_task.timestamp = ros::Time::now().toSec() * 1000;
    l_task.taskID = taskID;
    l_task.caseID = caseID;
    l_task.simResult = l_msgTypeZero;
    l_task.gkqResult = l_msgTypeOne;
    l_task.fusionResult = av_str;

    std::unique_lock<std::mutex> lck(m_firstTrajectotyMutex);
    m_firstTrajectoty = false;
    lck.unlock();
    l_task.taskStatus = 0;
    pub_taskStatus.publish(l_task);


    if(p_json["params"]["taskType"].asInt() == 0)
    {   l_task.taskStatus = 0;
        pub_taskStatus.publish(l_task);
        std::string shellpath;
        ros::param::get("shellpath",shellpath);
        std::string l_file = "sh "+ shellpath + "/restart.sh";
        std::cout<<"l_file:"<<l_file<<std::endl;
        int restartresult = std::system(l_file.c_str());
        if(restartresult == 0)
        {
            std::cout<<"gps restart success！"<<std::endl;
        }
        else
        {
            std::cout<<"gps restart failed"<<std::endl; 
        } 
        return ;
        
    }else if(p_json["params"]["taskType"].asInt() == 1)  //任务开始发送关键点数据
    {

        l_task.taskStatus = 1;
        pub_taskStatus.publish(l_task);
        usleep(10*1000);
        pub_simulationTrajectory.publish(m_Trajectories);
        pubTrajToDesicion();
    }else{
        l_task.taskStatus = 0;
        pub_taskStatus.publish(l_task);
    }


}

void redisInfo::deviceStatusMonitor(int p_deviceID)
{
    redisContext* sendContext = redisConnect(redis_ip.c_str(), redis_port);
    if (sendContext == nullptr || sendContext->err) {
        if (sendContext) {
            std::cerr << "Failed to connect to Redis: " << sendContext->errstr << std::endl;
        } else {
            std::cerr << "Failed to allocate redis sendContext." << std::endl;
        }
        //return 1;
    }
    redisReply* reply = nullptr;
    if(m_gateway)
    {
        const char* redis_pwd = "Wanji@300552!";
        reply = (redisReply*)redisCommand(sendContext, "AUTH %s", redis_pwd);
        if (reply == nullptr || reply->type == REDIS_REPLY_ERROR) {
            std::cout << "Failed to authenticate with Redis server" << std::endl;
            freeReplyObject(reply);
            redisFree(sendContext);
            // return -1;
        } 
    }
    while(1)
    {
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        auto millis = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();  
        // std::cout<<"p_deviceID:"<<p_deviceID<<std::endl;
        Json::Value jsonData;
        long l_time = ros::Time::now().toSec() * 1000;
        jsonData["timestamp"] = static_cast<Json::Int64>(millis);;
    
        
        long l_tmpTime  = ros::Time::now().toSec()*1000;
        std::unique_lock<std::mutex> lck(m_actuatorFlagMutex);
        int l_diffTime = l_tmpTime - m_actuatorTime ;
        lck.unlock();
    
        
        jsonData["deviceId"] = m_deviceIdNum;
        int l_state= 0;
        std::unique_lock<std::mutex> lck_tra(m_firstTrajectotyMutex);
        if(m_firstTrajectoty)
        {
            l_state = 1;
        }else
        {
            l_state = 0;
        }
        lck_tra.unlock();
        jsonData["state"] = 1;
        jsonData["type"] = l_state;    // 协议3.2  域控端设备上报 准备就绪
        Json::StreamWriterBuilder writerBuilder;
        std::string jsonString = Json::writeString(writerBuilder, jsonData);
        //发送融合信息
        std::string topic = "STATUSResult";
        reply = (redisReply*)redisCommand(sendContext, "PUBLISH %s %s", topic.c_str(), jsonString.c_str());
        if (reply == NULL) {
            std::cout << "PUBLISH "<<topic<<"  发布消息失败" << jsonString << std::endl;
        } else {
            if( reply->integer == 0)
            {
                ROS_WARN("CDJHS_STATUSResult 订阅者 %d",reply->integer);
            }
            std::cout << topic<<"- 1 -成功发布消息，已发送给 " << reply->integer << " 个订阅者" << jsonString<<std::endl;
            freeReplyObject(reply);
        }
        sleep(1);        
    }

}
//监控设备状态，将设备状态上报至云端
void redisInfo::deviceStatus(int p_deviceID)
{
    //
    auto l_tmp = std::thread(&redisInfo::deviceStatusMonitor, this, p_deviceID);
	l_tmp.detach();
    auto l_tmp1 = std::thread(&redisInfo::callbacktocloud, this);
	l_tmp1.detach();

}



void redisInfo::callbacktocloud()
{
   

    redisContext* sendContext = redisConnect(redis_ip.c_str(), redis_port);
    if (sendContext == nullptr || sendContext->err) {
        if (sendContext) {
            std::cerr << "Failed to connect to Redis: " << sendContext->errstr << std::endl;
        } else {
            std::cerr << "Failed to allocate redis sendContext." << std::endl;
        }
        //return 1;
    }
    redisReply* reply = nullptr;
    if(m_gateway)
    {
        const char* redis_pwd = "Wanji@300552!";
        reply = (redisReply*)redisCommand(sendContext, "AUTH %s", redis_pwd);
        if (reply == nullptr || reply->type == REDIS_REPLY_ERROR) {
            std::cout << "Failed to authenticate with Redis server" << std::endl;
            freeReplyObject(reply);
            redisFree(sendContext);
            // return -1;
        } 
    }
    while(1)
    {
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        auto millis = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();  
        // std::cout<<"p_deviceID:"<<p_deviceID<<std::endl;
        Json::Value jsonData;
        long l_time = ros::Time::now().toSec() * 1000;
        jsonData["timestamp"] = static_cast<Json::Int64>(millis);;
    
        jsonData["type"] = 0;
        long l_tmpTime  = ros::Time::now().toSec()*1000;
        std::unique_lock<std::mutex> lck(m_actuatorFlagMutex);
        int l_diffTime = l_tmpTime - m_actuatorTime ;
        lck.unlock();
    
        
        jsonData["deviceId"] = m_deviceIdNum;
      
        int l_state= 0;

        jsonData["state"] = 1;   //协议2.2  设备在线状态心跳
        
        Json::StreamWriterBuilder writerBuilder;
        std::string jsonString = Json::writeString(writerBuilder, jsonData);
        //发送融合信息
        std::string topic = "STATUSResult";
        reply = (redisReply*)redisCommand(sendContext, "PUBLISH %s %s", topic.c_str(), jsonString.c_str());
        if (reply == NULL) {
            std::cout << "PUBLISH "<<topic<<"  发布消息失败" << jsonString << std::endl;
        } else {
            if( reply->integer == 0)
            {
                ROS_WARN("CDJHS_STATUSResult 订阅者 %d",reply->integer);
            }
            std::cout << topic<<"- 1 -成功发布消息，已发送给 " << reply->integer << " 个订阅者" << jsonString<<std::endl;
            freeReplyObject(reply);
        }
        sleep(1);
   
    }

}



void redisInfo::pubTrajToDesicion()
{

    common_msgs::hdroute hdroutedata;
    int l_num = 0;
     if(m_Trajectories.value.size() % 2 !=0)
    {
       
        l_num = m_Trajectories.value.size() -1;
    }else{
        l_num = m_Trajectories.value.size();
    }

    int l_senceId = 0;
    for(int i = 0; i < l_num; i = i + 2)
    {
        common_msgs::hdmap hdmapdata;
        std::cout << "场景 " << l_senceId << "：" << std::endl;

        common_msgs::mapformat mappoint;
        mappoint.lon = m_Trajectories.value[i].longitude;
        mappoint.lat = m_Trajectories.value[i].latitude;
        mappoint.roadtype = 0;

        hdmapdata.point.push_back(mappoint);


        mappoint.lon = m_Trajectories.value[i + 1].longitude;
        mappoint.lat = m_Trajectories.value[i + 1].latitude;
        mappoint.roadtype = 2;
        hdmapdata.point.push_back(mappoint);

        hdmapdata.index = l_senceId;
        hdroutedata.map.push_back(hdmapdata);
        hdroutedata.timestamp = ros::Time::now().toSec()*1000;
        l_senceId++;
    }
    if(m_Trajectories.value.size() % 2 !=0)
    {
        common_msgs::hdmap hdmapdata;
        std::cout << "场景 " << l_senceId<< "：" << std::endl;

        common_msgs::mapformat mappoint;
        mappoint.lon = m_Trajectories.value[l_num].longitude;
        mappoint.lat = m_Trajectories.value[l_num].latitude;
        mappoint.roadtype = 0;

        hdmapdata.point.push_back(mappoint);


        mappoint.lon = m_Trajectories.value[l_num].longitude;
        mappoint.lat = m_Trajectories.value[l_num].latitude;
        mappoint.roadtype = 2;
        hdmapdata.point.push_back(mappoint);

        hdmapdata.index = l_senceId;
        hdroutedata.map.push_back(hdmapdata);
        hdroutedata.timestamp = ros::Time::now().toSec()*1000;
    }
    pub_pubTrajToDe.publish(hdroutedata);     
}