#pragma once
#include <ros/ros.h>
#include <ros/ros.h>
#include <jsoncpp/json/json.h>
#include "common_msgs/cloudpants.h"
#include "common_msgs/controllon.h"
#include "device_msgs/simulationPublish.h"
#include "device_msgs/mainCar.h"
#include"device_msgs/participantTrajectories.h"
#include "device_msgs/taskpublish.h"
#include "device_msgs/tessResult.h"
#include "device_msgs/taskstatus.h"
#include "common_msgs/hdroute.h"
#include "trajectoryInfo.h"
#include "imageInfo.h"
#include <queue>
#include <thread>
#include <openssl/md5.h>
class redisInfo
{
private:
  void deviceStatus(int p_deviceID);
  void deviceStatusMonitor(int p_deviceID);
  void monitorCloudData();
  
  void pubTaskStatus(Json::Value p_json);

public:
  redisInfo(ros::NodeHandle nh);
  ~redisInfo();
  void run();

  void SubCallback_taskResult(const device_msgs::tessResult::ConstPtr &msg);
  void SubCallback_actuator(const common_msgs::controllon::ConstPtr &msg);
  void callbacktocloud();
  void pubTrajToDesicion();
private:
  ros::Publisher pub_mainTrajectory;
  ros::Publisher pub_simulationTrajectory;
  ros::Publisher pub_startCompetition;
  ros::Publisher pub_taskStatus; //通知其它节点 话题名字以及是否开启任务
  ros::Subscriber sub_taskResult;
  ros::Subscriber sub_GQheartInfo; 
  ros::Subscriber sub_actuator;
  std::string redis_ip;
  int redis_port;
  std::string m_deviceId ;
  int m_deviceIdNum;
  std::string m_taskTopic;
  bool m_gateway = false;
  std::mutex m_cloudMsgMutex;
  std::queue<std::vector<std::string>> m_cloudMsgQue;

  trajectoryInfo *m_trajectory;
  
  
  std::string m_msgTypeTwo;

  device_msgs::participantTrajectories m_Trajectories;
  std::mutex m_actuatorFlagMutex;
  long m_actuatorTime;
  bool m_deviceStatus = false;
  std::mutex m_firstTrajectotyMutex;
  bool m_firstTrajectoty = false;
  ros::Publisher pub_pubTrajToDe; //通知其它节点 话题名字以及是否开启任务
};

