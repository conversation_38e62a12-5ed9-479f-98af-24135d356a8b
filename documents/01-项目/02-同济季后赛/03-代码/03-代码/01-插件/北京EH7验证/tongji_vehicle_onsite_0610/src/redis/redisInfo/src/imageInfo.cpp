#include "imageInfo.h"
imageInfo::imageInfo(std::string p_ip, int p_port, bool p_gateWay):m_ip(p_ip),m_port(p_port),m_gateway(p_gateWay)
{
   

    m_fileName = "/home/<USER>/config/image.json";

    m_shellFile = "/home/<USER>/jihousai/chajian";
    ros::param::get("shellpath",m_shellFile);
    ROS_INFO("imagepath:%s",m_shellFile.c_str());
}

imageInfo::~imageInfo()
{
}

void imageInfo::setImageID(std::string ID)
{
    m_deviceId = ID;
}

std::queue<imageIDInfo_t> imageInfo::reloadImageJson()
{
     std::vector<std::string> l_imageVec = getCmdList("docker images", 2);
    auto l_cloudLocal = readJson(m_fileName);
    std::queue<imageIDInfo_t> l_writeQue;
    //delete from json
    int l_size = l_cloudLocal.size();
    for(int i = 0; i < l_size; i++)
    {
        auto l_tmp = l_cloudLocal.front();
        
        l_cloudLocal.pop();
        if(l_tmp.p_localID != "")
        {
            for(int j = 0; j < l_imageVec.size(); j++)
            {
                if(l_tmp.p_localID == l_imageVec.at(j).substr(0,l_tmp.p_localID.size()))
                {
                    l_writeQue.push(l_tmp);
                }
            }
            
        }
    }
    writeJson(m_fileName,l_writeQue);
    return l_writeQue;
}

void imageInfo::deleteImageFileTar(Json::Value p_json)
{
    std::string l_imageID = p_json["imageId"].asString();
     
    std::string p_filePath;
    ros::param::get("imagepath",p_filePath);
    ROS_INFO("p_filePath:%s",p_filePath.c_str());
    std::string l_imagePath = "rm -rf " + p_filePath +  l_imageID + ".tar";
    auto l_are = exeCmd(l_imagePath);
}
// 删除镜像
void imageInfo::deleteImage(Json::Value p_json)
{
    
    ROS_INFO("start delete image process....");
    std::string l_imageID = p_json["imageId"].asString();
    std::string l_localID;
    //查看当前是否有此容器
 
    std::queue<imageIDInfo_t> l_imageList = readJson(m_fileName);
    int l_index = -1;
    int l_listSize = l_imageList.size();
    for(int i = 0; i< l_listSize; i++)
    {
        auto l_tmp = l_imageList.front();
        l_imageList.pop();
        if(l_imageID == l_tmp.p_cloudId)
        {
            l_index = i+1;
            l_localID = l_tmp.p_localID;
            deleteImage(l_imageID);
            break;
        }
    }
    if(l_index == -1)
    {
        ROS_WARN("not find l_image");
        return;
    }
    //删除容器
    std::string l_cmdcon = "docker rm -f "+l_localID;
    std::string l_strcon = exeCmd(l_cmdcon);
    std::cout<<"dele container str size:"<<l_strcon.size()<<"      "<<l_localID.size()<<std::endl;
    if(l_strcon.substr(0,l_localID.size()) == l_localID)
    {
        ROS_INFO("container delete success");
    }else
    {
        ROS_WARN("container delete failed");
    }
    //再删除镜像
    std::string l_cmdImg = "docker rmi "+l_localID;
    std::string l_strImg = exeCmd(l_cmdImg);

    //创建反馈消息

    Json::Value l_data;
    long l_time =ros::Time::now().toNSec();
    l_data["timestamp"] = std::to_string(l_time);
    l_data["deviceId"] = m_deviceId;
    l_data["imageId"] = l_imageID;
    
    std::string l_channel = "image_del_"+(m_deviceId)+"_result";
    
    if(l_strImg.find("sha256:") != -1)
    {
        ROS_INFO("image delete success");
        l_data["imageStatus"] = 1;
    }else
    {
        l_data["imageStatus"] = 0;
        ROS_WARN("image delete failed");
    }
    bool l_re = sendToRedis(l_data,l_channel);

}

//验证镜像
std::string imageInfo::calculateFileMD5(const std::string& filePath)
{
    std::ifstream file(filePath, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filePath << std::endl;
        return "";
    }

    MD5_CTX md5Context;
    MD5_Init(&md5Context);

    // 读取文件并更新MD5
    char buffer[4096];
    while (file.good()) {
        file.read(buffer, sizeof(buffer));
        size_t bytesRead = file.gcount();
        if (bytesRead > 0) {
            MD5_Update(&md5Context, buffer, bytesRead);
        }
    }

    // 计算最终的MD5摘要
    unsigned char result[MD5_DIGEST_LENGTH];
    int l_re = MD5_Final(result, &md5Context);

    // 将MD5摘要转换为16进制字符串
    std::stringstream ss;
    for (int i = 0; i < MD5_DIGEST_LENGTH; ++i) {
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(result[i]);
    }
    return ss.str();
}


struct ProgressData {
    double last_progress;
};

size_t writeData(void* ptr, size_t size, size_t nmemb, FILE* stream) {
    return fwrite(ptr, size, nmemb, stream);
}

int progressCallback(void* clientp, curl_off_t dltotal, curl_off_t dlnow, curl_off_t ultotal, curl_off_t ulnow) {
    ProgressData* progress = (ProgressData*)clientp;
    double download_progress = dlnow / (double) dltotal * 100.0;
    //std::cout << "Download progress: " << (double) ultotal << "   %  "    <<  ulnow << std::endl;
    if (download_progress != progress->last_progress) {
        std::cout << "Download progress: " << download_progress << "%" << std::endl;
        progress->last_progress = download_progress;
    }
    return 0;
}

//// 错误回调函数
//void curl_error(CURL *curl, CURLcode error) {
//    std::cout <<"##" << std::endl;
//    if(error != CURLE_OK) {
//        std::cerr << "Curl error: " << curl_easy_strerror(error) << std::endl;
//    }
//}
// 回调函数，用于处理数据
size_t WriteCallback(void *ptr, size_t size, size_t nmemb, void *userdata) {
    // 实现数据处理逻辑
    return size * nmemb;
}

void imageInfo::pullImageMonitor(Json::Value p_json)
{
    std::cout<<"pullImageMonitor"<<std::endl;
    //解析 文件路经，镜像id
    std::string l_imageID = p_json["imageId"].asString();
    std::string l_filePath = p_json["imgPath"].asString();
    // l_filePath = "/media/wanji/35cc35e0-b62c-4e3c-9a9d-2a596ca3bcdf/feishu/untitled1/python3.8.tar";

    //生成线程 拉取数据;
    auto l_tmp = std::thread(&imageInfo::containerRun, this, l_filePath, l_imageID);
	l_tmp.detach();
}

//下拉镜像，生成容器
void imageInfo::containerRun(std::string p_filePath, std::string p_imageID)
{
#if 1
   std::string l_imagePath;
    ROS_INFO("p_filePath:%s",p_filePath.c_str());
    int l_re = 1;
    // int l_re = pullImage(p_filePath, p_imageID,l_imagePath);
    ROS_ERROR("pullImage code :%d",l_re);
    std::string l_localImageID;
    if(!l_re)
    {
        Json::Value l_data;
        long l_time =ros::Time::now().toNSec();
        l_data["timestamp"] = std::to_string(l_time);
        l_data["deviceId"] = m_deviceId;
        l_data["imageId"] = p_imageID;
        l_data["imageStatus"] = (int)l_re;
        std::string l_channel = "CDJHS_ImageIssueResult";
        l_re = sendToRedis(l_data,l_channel);
        return;
    }
#endif
    // l_re = loadImage(l_imagePath,p_imageID);  //记载位置

    Json::Value l_data;
    long l_time =ros::Time::now().toNSec();
    l_data["timestamp"] = std::to_string(l_time);
    l_data["deviceId"] = m_deviceId;
    l_data["imageId"] = p_imageID;
    l_data["imageStatus"] = 1;//(int)l_re;
    std::string l_channel = "CDJHS_ImageIssueResult";
    l_re = sendToRedis(l_data,l_channel);
    // l_re = openContainer( "896");
    
}   

//从云端拉取数据
// 2 : image have exits
// 1: not have image and pull ok 
// 0: pull faile
// 3: have image but bot load 
int imageInfo::pullImage(std::string p_name, std::string p_imageID, std::string &p_path)
{
    CURL* curl;
    CURLcode res;
    FILE* file;
    ProgressData progress = { 2 };
    std::string output;
    curl_global_init(CURL_GLOBAL_ALL);
    curl = curl_easy_init();

    try {
        if (curl) {
            std::string l_root;
            ros::param::get("imagepath",l_root);
            p_path =l_root+p_imageID + ".tar";
            std::cout<<"p_path:"<<p_path<<std::endl;
            std::cout<<"p_name:"<<p_name<<std::endl;    
            file = fopen(p_path.c_str(), "wb");
            // curl_easy_setopt(curl, CURLOPT_URL, "https://www.python.org/ftp/python/3.8.12/Python-3.8.12.tgz");
   
            curl_easy_setopt(curl, CURLOPT_URL, p_name.c_str());  //对数据进行下载

            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, writeData);
          
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, file);
 
            curl_easy_setopt(curl, CURLOPT_NOPROGRESS, 0L);
          
            curl_easy_setopt(curl, CURLOPT_XFERINFOFUNCTION, progressCallback);
//            curl_easy_setopt(curl, CURLOPT_ERRORBUFFER, curl_error);
//            curl_easy_setopt(curl, CURLOPT_WRITEDATA, &output);
            curl_easy_setopt(curl, CURLOPT_XFERINFODATA, &progress);
             //设置连接超时时间为30秒
            curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 30000L);
            // 设置传输超时时间为300秒
            curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30000L);
            // 设置写数据回调函数
//            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
            // 执行请求
            CURLcode res = curl_easy_perform(curl);

            res = curl_easy_perform(curl);
            std::cout<<"11111111111111111111111111111"<<std::endl;
            if (res != CURLE_OK) {
                ROS_ERROR("down load failed code %d",res);
                std::cerr << "Error downloading file: " << curl_easy_strerror(res) << std::endl;
                return false;
            }
            curl_easy_cleanup(curl);
            fclose(file);
//            std::cout << output << std::endl;
        }
        curl_global_cleanup();
    } catch (const std::runtime_error& e) {
        std::cerr << "Caught an error: " << e.what() << std::endl;
    }




    return true;
}

//解析云端获取镜像列表，并将数据回传给云端
Json::Value imageInfo::parseImageListToCloud()
{
    std::cout<<"parseImageListToCloud"<<std::endl;
    Json::Value l_data;
   
    l_data["timestamp"] = ros::Time::now().toSec() * 1000;
    l_data["deviceId"] = m_deviceId;
    
    reloadImageJson();
    std::queue<imageIDInfo_t> l_image = readJson(m_fileName);
    int l_imageSize = l_image.size();
    for(int i = 0; i < l_imageSize; i++)
    {
        auto l_tmp = l_image.front();
        l_image.pop();
        l_data["imageList"].append(l_tmp.p_cloudId);
    }
    std::cout<<"l_image.size():"<<l_image.size()<<std::endl;
    if( l_image.size() == 0)
    {
        Json::Value location(Json::objectValue);
        l_data["imageList"].resize(0);
    }
    std::string l_channel = "CDJHS_ImageListResult";
    bool l_re = sendToRedis(l_data,l_channel);
    //传建发送通道，发送至redis
    return l_data;
}

bool imageInfo::loadImage(std::string p_name,std::string p_imageCloudID)
{

    auto l_imageList = getCmdList("docker images",2);


    for(int i = 0; i < l_imageList.size(); i++)
    {  
        {
            std::string l_cmdcon = "docker rm -f "+l_imageList.at(i);
            auto l_re = exeCmd(l_cmdcon);
            std::string l_cmd = "docker rmi -f "+l_imageList.at(i);
             l_re = exeCmd(l_cmd);
        }
    }
#if 0
    std::string shellname = "sh "+m_shellFile+"/loadimage.sh "+ p_name;
    std::cout<<"before ********************************"<<std::endl;
    std::cout<<"shellname:"<<shellname<<std::endl;
    int restartresult = std::system(shellname.c_str());
    std::cout<<"after ********************************"<<std::endl;
    if(restartresult == 0)
    {
        std::cout<<"run container shell success！"<<std::endl;
    }
    else
    {
    std::cout<<"run container shell false;"<<std::endl; 
    }
#endif  

#if 1
    // 使用popen执行curl命令
    std::string l_loadcmd = "docker load -i  "+p_name;
    std::cout<<"l_loadcmd:"<<p_name<<"     "<<p_imageCloudID<<std::endl;
    ROS_INFO("docker load cmd:%s",l_loadcmd.c_str());
    FILE *fp = popen(l_loadcmd.c_str(), "r");
    if (fp == nullptr) {
        std::cerr << "Failed to run curl command." << std::endl;
        return false;
    }
 
    // 读取curl命令的输出
    const int buffer_size = 128; // 根据需要调整缓冲区大小
    std::array<char, buffer_size> buffer;
    std::vector<std::string> l_imageVec;
    while (fgets(buffer.data(), buffer_size, fp) != nullptr) {
        std::cout<<"*********"<<buffer.data()<<std::endl;
        
    }
    std::cout<<"load callback:"<<buffer.data()<<std::endl;
    std::string l_reString =buffer.data();
    int l_pos = l_reString.find("Loaded image");
    // std::string l_localID = l_reString.substr(l_pos,12);
    int l_error = l_reString.find("Error");
    ROS_INFO("loaded image:%d, Error %d",l_pos, l_error);
    if(l_pos != -1 && l_error == -1)
    {
        ROS_INFO("%s  image load success  !\n",p_name.c_str());
        std::string l_localID = findNewLocalImageID();
        if(l_localID == "")
        {
           ROS_WARN("%s  image findNewLocalImageID  !\n",p_name.c_str());
           return false;
        }
        ROS_INFO("%s  findNewLocalImageID  !\n",l_localID.c_str());
        

    }else{
        ROS_WARN("%s  image load failed  !\n",p_name.c_str());
        return false;
    }
    // 关闭popen打开的文件指针
    int status = pclose(fp);
    if (status == -1) {
        std::cerr << "Error closing input stream." << std::endl;
        return false;
    }

#endif
    return true;
}

std::vector<std::string> imageInfo::getCmdList(std::string p_cmd, int p_index)
{
    // 使用popen执行curl命令
    FILE *fp = popen(p_cmd.c_str(), "r");   //docker ps -a
    if (fp == nullptr) {
        std::cerr << "Failed to run curl command." << std::endl;
        
    }
    // 读取curl命令的输出
    const int buffer_size = 128; // 根据需要调整缓冲区大小
    std::array<char, buffer_size> buffer;
    std::vector<std::string> l_imageVec;
    int l_lineIdex = 0;
    while (fgets(buffer.data(), buffer_size, fp) != nullptr) {
        std::istringstream iss(buffer.data());
        std::string  token;
        if(l_lineIdex == 0)
        {
            l_lineIdex++;
            continue;
        }
        int l_index = 0;
        while (iss >> token)
        {
            if(l_index == p_index)
            {
                l_imageVec.push_back(token);
            }
           l_index++;
        }
    }
    // 关闭popen打开的文件指针
    int status = pclose(fp);
    if (status == -1) {
        std::cerr << "Error closing input stream." << std::endl;
       
    }
    return l_imageVec;
}
//获取所有镜像ID --- 返回值含有第一行数据ID
std::vector<std::string> imageInfo::getImagesIDList()
{
    // 使用popen执行curl命令
    FILE *fp = popen("docker images", "r");   //docker ps -a
    if (fp == nullptr) {
        std::cerr << "Failed to run curl command." << std::endl;
        
    }
    // 读取curl命令的输出
    const int buffer_size = 128; // 根据需要调整缓冲区大小
    std::array<char, buffer_size> buffer;
    std::vector<std::string> l_imageVec;
    int l_lindNum = 0;
    while (fgets(buffer.data(), buffer_size, fp) != nullptr) {
        std::istringstream iss(buffer.data());
        std::string  token;
        int l_index = 0;
        if(l_lindNum <= 0)
        {
            l_lindNum++;
            continue;
        }
        while (iss >> token)
        {
            if(l_index == 2)
            {
                l_imageVec.push_back(token);
            }
           l_index++;
        }
        l_lindNum++;
    }
    // 关闭popen打开的文件指针
    int status = pclose(fp);
    if (status == -1) {
        std::cerr << "Error closing input stream." << std::endl;
       
    }
    return l_imageVec;
}

bool imageInfo::deleteContainer(std::string p_name)
{
    ROS_INFO("delete open container");
    std::string l_deteCmd = "docker rm -f "+ p_name;
        // 使用popen执行curl命令
    FILE *fp = popen(l_deteCmd.c_str(), "r");   //docker ps -a
    if (fp == nullptr) {
        std::cerr << "Failed to run curl command." << std::endl;
        
    }
 
    // 读取curl命令的输出
    const int buffer_size = 128; // 根据需要调整缓冲区大小
    std::array<char, buffer_size> buffer;

    int l_index = 0;
    while (fgets(buffer.data(), buffer_size, fp) != nullptr) {
        std::istringstream iss(buffer.data());
        std::string  token;
        
        while (iss >> token)
        {
            if(token == p_name)
            {
                l_index++;
                ROS_WARN("delete %d container",p_name.c_str());
            }
           
        }
    }
    // 关闭popen打开的文件指针
    int status = pclose(fp);
    if (status == -1) {
        std::cerr << "Error closing input stream." << std::endl;
    }
    if(l_index == 0)
    {
        ROS_ERROR("delete %d container failed",p_name.c_str());
        return false;
    }
    return true;
}
//sudo nvidia-docker run --runtime=nvidia --shm-size=15G -tid --net=host --name="auto" --privileged=true -e NVIDIA_VISIBLE_DEVICES=0 -e.UTF-8  -v /tmp/.X11-unix:/tmp/.X11-unix -e DISPLAY=:0 fce7ee46eb41 bash
//创建和镜像名字一样的 容器
bool imageInfo::createContainer(std::string p_name)
{
    sleep(10);
     std::cout<<"createContainer"<<std::endl;
    //容器命名+与主机共享IP
    //docker run -it --name=f67d8dfd7883 f67d8dfd7883

    //docker run
    // std::string l_cmd = "docker run -d --name=onsitev "+p_name;
    std::string l_cmd  = "docker run -d --name=onsitev 2f9ccc897a85";

    // std::string l_cmd = "nvidia-docker run --runtime=nvidia --shm-size=15G -tid --net=host --name="+p_name +" --privileged=true -e NVIDIA_VISIBLE_DEVICES=0 -e.UTF-8  -v /tmp/.X11-unix:/tmp/.X11-unix -e DISPLAY=:0 "+p_name;
    // std::string l_cmd = "docker run  --shm-size=15G -tid --net=host --name="+p_name +" --privileged=true -e NVIDIA_VISIBLE_DEVICES=0 -e.UTF-8  -v /tmp/.X11-unix:/tmp/.X11-unix -e DISPLAY=:0 "+p_name;
   
    // std::string l_cmd ="docker run -itd --net=host -v /home/<USER>/Downloads/onsite-structured-test-master:/onsite-structured-test/ --name="+p_name +" "+p_name+" bash";
    std::string l_name = std::to_string(ros::Time::now().toNSec()) ;
    std::string shellname = "sh " + m_shellFile+ "/runstart.sh "+ l_name + " "+p_name;
    std::cout<<"before ********************************"<<std::endl;
    std::cout<<"l_cmd shellname:"<<shellname<<std::endl;
    int restartresult = std::system(shellname.c_str());
    // int restartresult = std::system(l_cmd.c_str());
    std::cout<<"after ********************************"<<std::endl;
    if(restartresult == 0)
    {
        std::cout<<"run container shell success！"<<std::endl;
        return true;
    }
    else
    {
    std::cout<<"run container shell false;"<<std::endl; 
    return false;
    }  
    
 #if 0   
    std::string l_cmd ="docker run -itd --net=host --name="+p_name +" "+p_name+" bash";
    
    FILE *fp = popen(l_cmd.c_str(), "r");
    if (fp == nullptr) {
        std::cerr << "Failed to run curl command." << std::endl;
        return 1;
    }
 
    // 有一些异常判断没有加   
    const int buffer_size = 128; // 根据需要调整缓冲区大小
    std::array<char, buffer_size> buffer;
    while (fgets(buffer.data(), buffer_size, fp) != nullptr) {
        
        
    }

    //
    std::cout<<"buffer.data():"<<buffer.data()<<std::endl;

    auto l_images = getCmdList("docker ps -a", 1);
    int l_imageSize = l_images.size();
    for(int i = 0; i< l_imageSize; i++)
    {
        std::string l_ID = l_images.at(i);
        if(l_ID.substr(0,p_name.length()) == p_name)
        {
            ROS_INFO("%s  container create success  !\n",p_name.c_str());
        }
    }

    // 关闭popen打开的文件指针
    int status = pclose(fp);
    if (status == -1) {
        std::cerr << "Error closing input stream." << std::endl;
        return 1;
    }
    #endif
    return true;
}
bool imageInfo::stopContainer()
{
    ROS_INFO("fine open container");
        // 使用popen执行curl命令
    FILE *fp = popen("docker ps", "r");   //docker ps -a
    if (fp == nullptr) {
        std::cerr << "Failed to run curl command." << std::endl;
        
    }
 
    // 读取curl命令的输出
    const int buffer_size = 128; // 根据需要调整缓冲区大小
    std::array<char, buffer_size> buffer;
    std::vector<std::string> l_imageVec;
    while (fgets(buffer.data(), buffer_size, fp) != nullptr) {
        std::istringstream iss(buffer.data());

        std::string  token;
        int l_index = 0;
        while (iss >> token)
        {
            if(l_index == 1)
            {
                l_imageVec.push_back(token);
            }
           l_index++;
        }
    }
    // 关闭popen打开的文件指针
    int status = pclose(fp);
    if (status == -1) {
        std::cerr << "Error closing input stream." << std::endl;

    }

    FILE *fp_stop = popen("docker ps", "r");   //docker ps -a
    if (fp_stop == nullptr) {
        std::cerr << "Failed to run curl command." << std::endl;
    }
    bool l_deteRe = false;
    for(int i = 1; i< l_imageVec.size(); i++)
    {
        bool l_stopRE = stopContainer(l_imageVec.at(i));
        ROS_ERROR("stopContainer result %d",l_stopRE);
        if(l_stopRE)
        {
            l_deteRe = deleteContainer(l_imageVec.at(i));
            if(l_deteRe)
            {
                return l_deteRe;
            }
        }
    }
}

bool imageInfo::stopContainer(std::string p_name)
{
    ROS_INFO("delete open container");
    std::string l_cmd = "docker stop "+p_name;
    FILE *fp = popen(l_cmd.c_str() , "r");   //docker ps -a
    if (fp == nullptr) {
        std::cerr << "Failed to run curl command." << std::endl;
        
    }
 
    // 读取curl命令的输出
    const int buffer_size = 128; // 根据需要调整缓冲区大小
    std::array<char, buffer_size> buffer;
    int l_index = 0;
    while (fgets(buffer.data(), buffer_size, fp) != nullptr) {
        std::istringstream iss(buffer.data());
        std::string  token;
        
        while (iss >> token)
        {
            if(p_name == token)
            {
                ROS_WARN("%s stop success",p_name.c_str());
                l_index++;
            }
           
        }
    }
    // 关闭popen打开的文件指针
    int status = pclose(fp);
    if (status == -1) {
        std::cerr << "Error closing input stream." << std::endl;

    }
    if(l_index == 0)
    {
        return false;
    }
    return true;
}



// ************:18082/images/fsdownload.zip
bool imageInfo::openContainer(std::string p_name)
{
    std::cout<<"*************************openContainer"<<std::endl;
    auto l_imageVec = getImagesIDList();
    bool l_haveFlag = false;
    std::string l_localID;
    bool l_re = false;
    std::cout<<"l_imageVec.size:"<<l_imageVec.size()<<std::endl;
    for(int i = 0; i < l_imageVec.size(); i++)
    {
        ROS_INFO("%s image ID",l_imageVec.at(i).c_str());
        l_re = createContainer(l_imageVec.at(i));
        l_localID = l_imageVec.at(i);
    }
    return l_re;
#if 0
    if(l_imageVec.size() <= 0)
    {
        return false;
    }
    std::string l_cmd = "docker start "+l_imageVec.at(0);
    ROS_INFO("%s",l_cmd.c_str());
    FILE *fp = popen(l_cmd.c_str(), "r");
    if (fp == nullptr) {
        std::cerr << "Failed to run curl command." << std::endl;
        return 1;
    }
 
    // 读取curl命令的输出
    const int buffer_size = 128; // 根据需要调整缓冲区大小
    std::array<char, buffer_size> buffer;
    while (fgets(buffer.data(), buffer_size, fp) != nullptr) {
        
        
    }
    std::cout<<"buffer.data():"<<buffer.data()<<std::endl;
    std::string str(buffer.data());
    if(str.substr(0,l_localID.length()) != l_localID)
    {
        // 关闭popen打开的文件指针
        int status = pclose(fp);
        if (status == -1) {
            std::cerr << "Error closing input stream." << std::endl;
            return 1;
        }
        ROS_WARN("%s  is not exisit  !\n",l_localID.c_str());
        
        return false;
    }else
    {
        ROS_INFO("%s  open success  !\n",l_localID.c_str());
    }
    // 关闭popen打开的文件指针
    int status = pclose(fp);
    if (status == -1) {
        std::cerr << "Error closing input stream." << std::endl;
        return 1;
    }
#endif
}

//使用popen指令 
std::string imageInfo::exeCmd(std::string p_info)
{
    std::cout<<"p_info:"<<p_info<<std::endl;
    ROS_WARN("%s",p_info.c_str());
    FILE *fp = popen(p_info.c_str(), "r");
    if (fp == nullptr) {
        std::cerr << "Failed to run curl command." << std::endl;
    }
 
    // 读取curl命令的输出
    const int buffer_size = 128; // 根据需要调整缓冲区大小
    std::array<char, buffer_size> buffer;
    while (fgets(buffer.data(), buffer_size, fp) != nullptr) {
        
        
    }
    std::cout<<"buffer.data():"<<buffer.data()<<std::endl;
    std::string str(buffer.data());

    // 关闭popen打开的文件指针
    int status = pclose(fp);
    if (status == -1) {
        std::cerr << "Error closing input stream." << std::endl;
    }
    return str; 
}

bool imageInfo::sendToRedis(Json::Value p_json, std::string p_channel)
{
    redisContext* context = redisConnect(m_ip.c_str(), m_port);
    if (context == nullptr || context->err) {
        if (context) {
            std::cerr << "Failed to connect to Redis: " << context->errstr << std::endl;
        } else {
            std::cerr << "Failed to allocate redis context." << std::endl;
            return false;
        }
        
    }
    redisReply* reply = nullptr;
    if(m_gateway)
    {
        const char* redis_pwd = "Wanji@300552!";
        reply = (redisReply*)redisCommand(context, "AUTH %s", redis_pwd);
        if (reply == nullptr || reply->type == REDIS_REPLY_ERROR) {
            std::cout << "Failed to authenticate with Redis server" << std::endl;
            freeReplyObject(reply);
            redisFree(context);
            return false;
        } 

    }
    Json::StreamWriterBuilder writerBuilder;
    std::string jsonString = Json::writeString(writerBuilder, p_json);
    std::cout<<"jsonString:"<<jsonString<<std::endl;
    reply = (redisReply*)redisCommand(context, "PUBLISH %s %s", p_channel.c_str(),jsonString.c_str());
    // reply = (redisReply*)redisCommand(context, "PUBLISH CA001MatchResultData %s", jsonString.c_str());

    if (reply == NULL) {
        ROS_WARN("send %s to cloud is failed",p_channel.c_str());
        return false;
    } else {
       
        ROS_INFO("send %s to cloud is success, subscribe %lld",p_channel.c_str(),reply->integer);
        freeReplyObject(reply);
    }
    return true;
}


std::queue<imageIDInfo_t> imageInfo::readJson(const std::string p_fileName)
{
    std::queue<imageIDInfo_t> l_queue;
    std::ifstream file(p_fileName);
    Json::Value root;
    Json::CharReaderBuilder l_builder;
    std::string errs;
    bool l_re = Json::parseFromStream(l_builder, file, &root, &errs);
    if(!l_re)
    {
        ROS_WARN("parse %s faile",p_fileName.c_str());
    }

    int i = 0;
    for(const auto& item : root) 
    {
        std::cout<<"item:"<<item<<std::endl;
        for(const auto& l_item : item)
        {
            imageIDInfo_t l_image;
            l_image.p_cloudId = l_item["imagecloudID"].asString();
            l_image.p_localID = l_item["imagelocalID"].asString();
            std::cout<<"readjson:"<<l_image.p_localID <<std::endl;
            l_queue.push(l_image);            
        }

    }
    std::cout<<"l_queue.size:"<<l_queue.size()<<std::endl;
    return l_queue;
}


void imageInfo::writeJson(const std::string p_fileName,std::queue<imageIDInfo_t> p_image)
{

    Json::Value root;
    
    if(p_image.size() <= 0)
    {
        return;
    }
    while (!p_image.empty())
    {
        Json::Value l_obj;
        auto l_tmp = p_image.front();
        p_image.pop();
        l_obj["imagecloudID"] = l_tmp.p_cloudId;
        l_obj["imagelocalID"] = l_tmp.p_localID;
        root["image"].append(l_obj);
    }
    
    std::ofstream file(p_fileName);
    Json::StreamWriterBuilder l_writeBuild;
    std::unique_ptr<Json::StreamWriter> writer(l_writeBuild.newStreamWriter());
    writer->write(root, &file);
    file.close();
    std::cout<<"writeJson writeJson writeJson"<<std::endl;
}


void imageInfo::saveImage(std::string p_imageID, std::string p_localImageID)
{
    //read Image
    std::queue<imageIDInfo_t> l_que;
    l_que = readJson(m_fileName);
    imageIDInfo_t l_imag;
    l_imag.p_cloudId = p_imageID;
    l_imag.p_localID = p_localImageID;
    l_que.push(l_imag);
    writeJson(m_fileName, l_que);
    ROS_INFO("save new image %s success",p_imageID.c_str());
}

void imageInfo::deleteImage(std::string p_cloudImageID)
{
    std::queue<imageIDInfo_t> l_que;
    l_que = readJson(m_fileName);
    if(l_que.size() <=0)
    {
        ROS_WARN("%s image is not exist",p_cloudImageID);
        return;
    }
    std::queue<imageIDInfo_t> l_copyque;
    while (!l_que.empty())
    {
        auto l_tmp = l_que.front();
        l_que.pop();
        if(l_tmp.p_cloudId == p_cloudImageID)
        {
            ROS_INFO("delete %s image",p_cloudImageID);
        }else
        {
            l_copyque.push(l_tmp);
        }
    }
    writeJson(m_fileName, l_copyque);
}

std::string imageInfo::findNewLocalImageID()
{
    std::cout<<"findNewLocalImageID"<<std::endl;
    // 使用popen执行curl命令
    FILE *fp = popen("docker images", "r");   //docker ps -a
    if (fp == nullptr) {
        std::cerr << "Failed to run curl command." << std::endl;
        
    }
 
    // 读取curl命令的输出
    const int buffer_size = 128; // 根据需要调整缓冲区大小
    std::array<char, buffer_size> buffer;
    std::vector<std::string> l_imageVec;
    while (fgets(buffer.data(), buffer_size, fp) != nullptr) {
        std::istringstream iss(buffer.data());
        std::string  token;
        int l_index = 0;
        while (iss >> token)
        {
            
            if(l_index == 2)
            {
                l_imageVec.push_back(token);
                std::cout<<"token:"<<token<<std::endl;
            }
           l_index++;
        }
    }
    // 关闭popen打开的文件指针
    int status = pclose(fp);
    if (status == -1) {
        std::cerr << "Error closing input stream." << std::endl;
       
    }

    if(l_imageVec.size() > 0)
    {
        return l_imageVec.at(0);
    }else{
        return "";
    }
    
}


bool imageInfo::findImageID(const std::string p_cloudImageID){
    auto l_imageQue = readJson(m_fileName);
    while(!l_imageQue.empty())
    {
        auto l_tmp = l_imageQue.front();
        l_imageQue.pop();
        std::string l_cloudId = l_tmp.p_cloudId; 
        if(p_cloudImageID == l_cloudId)
        {
            return true;
        }
      
    }
    return false;
}
std::string  imageInfo::findlocalImageID(const std::string p_cloudImageID){
    auto l_imageQue = readJson(m_fileName);
    while(!l_imageQue.empty())
    {
        auto l_tmp = l_imageQue.front();
        l_imageQue.pop();
        std::string l_cloudId = l_tmp.p_cloudId; 
        if(p_cloudImageID == l_cloudId)
        {
            return l_tmp.p_localID;
        }
      
    }
    
}



bool imageInfo::findImageDir( std::string p_imageID)
{
    std::string l_root;
    ros::param::get("imagepath",l_root);
    namespace fs = std::filesystem;
    fs::path dir_Path = l_root;
    if(!fs::exists(dir_Path))
    {
        return false;
    }
    for(const auto& entry: fs::directory_iterator(dir_Path))
    {
        if(entry.is_regular_file() && entry.path().extension() == ".tar")
        {
            std::cout<<"filenames:"<<entry.path().filename()<<std::endl;
            if(entry.path().filename() == p_imageID)
            {
                return true;
            }
        }
    }
    return false;
}