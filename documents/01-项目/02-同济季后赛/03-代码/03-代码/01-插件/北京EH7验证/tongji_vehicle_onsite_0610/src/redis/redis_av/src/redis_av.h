#include <ros/ros.h>
#include <jsoncpp/json/json.h>
#include "common_msgs/sensorgps.h"
#include "common_msgs/actuator.h"
#include "common_msgs/controllat.h"
#include "common_msgs/controllon.h"
#include "common_msgs/sensorobjects.h"
#include "common_msgs/controlcmd.h"
#include "common_msgs/decisionbehavior.h"
#include "device_msgs/taskstatus.h"
#include <thread>
//发送自车消息到redis
class Redis_av
{
private:
  void SubCallback_gps(const common_msgs::sensorgps::ConstPtr &msg);
  void SubCallback_actuator(const common_msgs::actuator::ConstPtr &msg);


  void SubCallback_controllon(const common_msgs::controlcmd::ConstPtr &msg);
  void SubCallback_taskstatus(const device_msgs::taskstatus::ConstPtr &msg);
  void SubCallback_wanjiObject(const common_msgs::sensorobjects::ConstPtr &msg);
  void SubCallback_decisionbehavior(const common_msgs::decisionbehavior::ConstPtr &msg);
  common_msgs::actuator       m_actuatormsg;

private: 
    ros::Subscriber sub_gps;
    ros::Subscriber sub_actuator;
    ros::Subscriber sub_path;
    ros::Subscriber sub_gpsStatus;
    ros::Subscriber sub_obuStatus;
    ros::Subscriber sub_controllon;
    ros::Subscriber sub_taskStatus;
    ros::Subscriber sub_decisionbehavior;
    ros::Subscriber sub_objects;
    ros::Publisher pub_controllonWj;
    ros::Publisher pub_controllatWj;
    common_msgs::sensorgps      m_currentGps;
    common_msgs::controlcmd       m_currentControllon;
    common_msgs::decisionbehavior m_decisionbehavior;
    common_msgs::sensorobjects m_sensorobjects;
    std::mutex m_currentGpsMutex;
    std::mutex m_currentActuatorMutex;
    std::mutex m_currentPathMutex;
    std::mutex m_currentGpsStatusMutex;
    std::mutex m_currentObuStatusMutex;
    std::mutex m_currentControllonMutex;
    int count;
    std::string redis_ip;
    int redis_port;
    std::string m_subname;
    bool m_gateway = true;
    device_msgs::taskstatus m_taskStatus;
    int m_deviceID;
    bool  m_online = false;
public: 
    Redis_av(ros::NodeHandle nh);
    ~Redis_av();
    void AVToCloudTH();

};