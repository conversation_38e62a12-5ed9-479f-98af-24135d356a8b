#include "redisInfo.h"
#include "imageInfo.h"
#include <unistd.h>
#include "authDate.hpp"

int test()
{
    std::string l_reString = "Loaded image ID: sha256:1289dfd9175a96949bfe1a75c3eaccc82a4ed95e4eab90df3ec3941ce063e9e6";
    int l_pos = l_reString.find("sha256:");
    std::cout<<"l_pos:"<<l_pos<<std::endl;


#if 0
    int euid = 0;
    int uid = getuid();
    std::cout<<"euid:"<<euid<<std::endl;
    std::cout<<"uid:"<<uid<<std::endl;
    int result = seteuid(euid);
    if(result != 0)
    {
        std::cout<<"error !!!!!"<<std::endl;
    }

    // 使用popen执行curl命令
    FILE *fp = popen("docker load -i  /media/wanji/************************************/feishu/untitled1/python3.8.tar", "r");
    if (fp == nullptr) {
        std::cerr << "Failed to run curl command." << std::endl;
        return 1;
    }
 
    // 读取curl命令的输出
    const int buffer_size = 128; // 根据需要调整缓冲区大小
    std::array<char, buffer_size> buffer;
    std::vector<std::string> l_imageVec;
    while (fgets(buffer.data(), buffer_size, fp) != nullptr) {
        std::cout<<"*********"<<buffer.data()<<std::endl;
        
    }

    
    // 关闭popen打开的文件指针
    int status = pclose(fp);
    if (status == -1) {
        std::cerr << "Error closing input stream." << std::endl;
        return 1;
    }

    result = seteuid(uid);
    if(result != 0)
    {
        std::cout<<"error !!!!!"<<std::endl;
    }
#endif
    return 0;
}


int main(int argc,char *argv[])
{
    if (isExpired())
    {
        std::cerr << "授权已过期！\n"
                  << std::endl;
        return 1;
    }
    // int l_re = test();
    // imageInfo *m_imageInfo;
    // m_imageInfo = new imageInfo("10.100.12.21", 8666, false);
    // std::string l_new = m_imageInfo->findNewLocalImageID();
    // bool re = m_imageInfo->createContainer(l_new);
    // m_imageInfo->saveImage("1324huieu", l_new);
    // std::cout<<"new find :"<<re<<std::endl;
#if 1
    ros::init(argc,argv,"redisInfodata");
    ros::NodeHandle nh;
    redisInfo node(nh);

    std::cerr << "RedisInfo init ok." << std::endl;
    // node.run();
    ros::spin();
#endif
    
    return 0;
}