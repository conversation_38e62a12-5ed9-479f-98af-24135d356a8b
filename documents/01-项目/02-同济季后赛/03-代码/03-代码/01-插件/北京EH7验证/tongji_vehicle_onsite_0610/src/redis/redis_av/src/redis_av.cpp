#include <ros/ros.h>
#include <iostream>
#include <string>
#include <hiredis/hiredis.h>
#include <jsoncpp/json/json.h>
#include"redis_av.h"
#include <fcntl.h>
#include <sstream>
#include <std_msgs/String.h>
#include <unistd.h>
redisContext* redis;
Json::Value statejson;
int startflag = 0;
int Protocolmode = 0;
int flag =0;
int delayflag = 0 ;
Redis_av::Redis_av(ros::NodeHandle nh)
{
    std::cout<<"构造函数"<<std::endl;
    // memset(&m_currentGps, 0, sizeof(m_currentGps));
    // memset(&m_actuatormsg, 0, sizeof(m_actuatormsg));
 
    memset(&m_currentControllon, 0, sizeof(m_currentControllon));
    std::string node_name = ros::this_node::getName();
    ros::param::get("redis_ip",redis_ip);
    ros::param::get("redis_port",redis_port);
    nh.param(node_name+"/gateway",m_gateway,m_gateway);
    ros::param::get("deviceId",m_deviceID);
    ros::param::get("online",m_online);
    std::cout<<"redis_ip:"<<redis_ip<<std::endl;
    std::cout<<"redis_port:"<<redis_port<<std::endl;
    sub_gps        = nh.subscribe("sensorgps", 2, &Redis_av::SubCallback_gps, this);
    sub_actuator   = nh.subscribe("/actuator", 2, &Redis_av::SubCallback_actuator, this);
    sub_controllon = nh.subscribe("/controlcmd", 2, &Redis_av::SubCallback_controllon, this);
    sub_taskStatus = nh.subscribe("/taskstatus", 2, &Redis_av::SubCallback_taskstatus, this);
    sub_decisionbehavior = nh.subscribe("/decisionbehavior" ,2, &Redis_av::SubCallback_decisionbehavior,this);
    sub_objects = nh.subscribe("/objectTrack/track_results", 2, &Redis_av::SubCallback_wanjiObject, this);
    pub_controllonWj = nh.advertise<common_msgs::controllon>("/controllon", 10);
    pub_controllatWj = nh.advertise<common_msgs::controllat>("/controllat", 10);
    // auto l_tmp = std::thread(&Redis_av::AVToCloudTH, this);
    // l_tmp.detach();
}
Redis_av::~Redis_av()
{
    std::cout<<"析构函数"<<std::endl;
    //   // 断开连接
    // redisFree(redis);
}



//主车轨迹上报
void Redis_av::AVToCloudTH()
{
    ros::Rate rate(10);

    int frameCount = 0;
    // 连接到Redis服务器
    // redisContext* context = redisConnect("************", 5010);//tongji
    redisContext* context = redisConnect(redis_ip.c_str(), redis_port);
    if (context == nullptr || context->err) {
        if (context) {
            std::cerr << "Failed to connect to Redis: " << context->errstr << std::endl;
        } else {
            std::cerr << "Failed to allocate redis context." << std::endl;
        }
        //return 1;
    }
    redisReply* reply = nullptr;
    if(m_gateway)
    {
        const char* redis_pwd = "Wanji@300552!";
        reply = (redisReply*)redisCommand(context, "AUTH %s", redis_pwd);
        if (reply == nullptr || reply->type == REDIS_REPLY_ERROR) {
            std::cout << "Failed to authenticate with Redis server" << std::endl;
            freeReplyObject(reply);
            redisFree(context);
            // return -1;
        } 
    }

    while (ros::ok())
    {
        std::cout<<"111111"<<std::endl;

        common_msgs::sensorgps       l_currentGps;
        common_msgs::actuator        l_currentActuator;
        common_msgs::controlcmd      l_currentControllon;

        {
            std::unique_lock<std::mutex> lk(m_currentGpsMutex);
            l_currentGps = m_currentGps;
        }
        {
            std::unique_lock<std::mutex> lk(m_currentActuatorMutex);
            l_currentActuator = m_actuatormsg;
            lk.unlock();
        }
        {
            std::unique_lock<std::mutex> lk(m_currentControllonMutex);
            l_currentControllon = m_currentControllon;
        }

   
        // 创建一个空的JSON对象Json::Value jsonData;
        Json::Value carData;
        carData["type"] = "trajectory";

        Json::Value jsonData;

        char buffer[80];
        struct timeval tv; 
		gettimeofday(&tv,NULL);
        std::strftime(buffer, 80, "%Y-%m-%d %H:%M:%S", std::localtime(&tv.tv_sec));

        std::string mystamp(buffer);
        
        // jsonData["type"] = "trajectory";
        long l_time =ros::Time::now().toSec()*1000;
        jsonData["timestampType"] = "CREATE_TIME";
        jsonData["timestamp"] = std::to_string(l_time);
        // jsonData["timestampType"] = "CREATE_TIME";
        // 添加一个string值taskTypemp;
         // 创建一个包含多个经度和纬度的JSON数组
        Json::Value jsonArray(Json::arrayValue);  
        Json::Value location(Json::objectValue);
        l_time =ros::Time::now().toSec()*1000;
        location["timestamp"] = std::to_string(l_time);;
        std::string globalTimeStamp = std::to_string(l_time);
        location["globalTimeStamp"] = globalTimeStamp;
        location["frameId"] = frameCount;
        std::string id = "1";
        // id = "1";
        location["id"] = m_deviceID;
      
        location["name"] = "vanjee9";
        std::string picLicense = "";
        location["picLicense"] = picLicense;
        // 添加一个int值
        location["originalColor"] = 3;
        // 添加一个int值
        location["vehicleColor"] = 0;
        // 添加一个int值
        location["vehicleType"] = 1;
        // 添加一个int值
        location["length"] = 476;
        // 添加一个int值
        location["width"] = 190;
        // 添加一个int值
        location["height"] = 170;

        
        location["driveType"] = 1;

        location["autoStatus"] = 1;
        switch(l_currentActuator.sysstatus) // 2 紧急制动
        {
            case 0: // 0 人工驾驶
                location["autoStatus"] = 0;
                // 当前驾驶模式（0：自动驾驶:1：人工驾驶:2：远程驾驶）
                break;
            case 1: // 1 自动驾驶
                location["autoStatus"] = 1;
        
                break;
            default: // 2 紧急制动
                location["autoStatus"] = 2;
             
                break;
        }

        double longitude = l_currentGps.lon;
        location["longitude"] = longitude;
        double latitude = l_currentGps.lat;
        location["latitude"] = latitude;
        location["longitude"] = longitude;
        location["latitude"] = latitude;
        location["courseAngle"] = l_currentGps.heading;
        
        location["speed"] = l_currentGps.velocity * 3.6;

        location["lonAcc"] =  l_currentGps.accx;
        location["latAcc"] = l_currentGps.accy;
        location["angularVelocityX"] = l_currentGps.velocity;
        location["gear"] = l_currentActuator.gear;
        location["steeringWheelAngle"] = l_currentControllon.wheel_angle ;//l_currentActuator.wheel_angle;
        
        if(m_currentControllon.acceleration >0)
        {
            location["acceleratorPedal"]= m_currentControllon.acceleration ;
            location["braking"]= 0;
        }else
        {
             location["acceleratorPedal"]= 0;
            location["braking"]= m_currentControllon.acceleration ;           
        }
        
        location["indicatorStatus"]= m_decisionbehavior.turnlights;
        location["isPerception"] = 0; //主车数据
        if(m_online)
        {

        }
        jsonData["value"].append(location);
        //实车状态下 需要发送自车感知到的数据
        if(m_online)
        {   
            auto p_object = m_sensorobjects;
            int l_size = p_object.obs.size();
            for(int i = 0; i < l_size; i++)
            {
                Json::Value objectArray;  
                long l_time = ros::Time::now().toSec()*1000;
                objectArray["timestamp"] =  std::to_string(l_time);
                objectArray["globalTimeStamp"]= std::to_string(l_time);
                objectArray["id"] = p_object.obs[i].id;
                objectArray["name"]= p_object.obs[i].id;
                objectArray["length"] = p_object.obs[i].length;
                objectArray["width"] = p_object.obs[i].width;
                objectArray["height"] = p_object.obs[i].height;


            
                objectArray["longitude"] = p_object.obs[i].longtitude;//p_object.objs[i].xabs;  //相对位置信息
                objectArray["latitude"] = p_object.obs[i].latitude;//p_object.objs[i].yabs;    //相对位置信息
                objectArray["relative_x"] = p_object.obs[i].relspeedx;    //相对位置信息
                objectArray["relative_y"] = p_object.obs[i].relspeedy;    //相对位置信息
                objectArray["relative_z"] = 0;    //相对位置信息
                objectArray["courseAngle"] = p_object.obs[i].azimuth * 180 / M_PI - l_currentGps.heading;
                std::cout<<"objectArray[courseAngle]:"<< p_object.obs[i].id<<"    "<<objectArray["courseAngle"]<<"    "<<p_object.obs[i].azimuth<<std::endl;
                
                
                objectArray["courseAngle"] = p_object.obs[i].azimuth * 180 / M_PI - m_currentGps.heading;;
                if(objectArray["courseAngle"] < 0)
                {
                    std::cout<<"heading gap < 0 "<< std::endl;
                    objectArray["courseAngle"] = p_object.obs[i].azimuth * 180 / M_PI - m_currentGps.heading + 360;
                }
                
                float l_speed = p_object.obs[i].speeds * p_object.obs[i].speeds + p_object.obs[i].speedl * p_object.obs[i].speedl;
                objectArray["speed"] = sqrt(l_speed) * 3.6;   //相对速度信息
                objectArray["vehicleType"] = p_object.obs[i].classification;
                objectArray["isPerception"] = 1; //从车数据
                jsonData["value"].append(objectArray);
            }
        }
        
        carData["value"] =jsonData;
        Json::StreamWriterBuilder writerBuilder;
        std::string jsonString = Json::writeString(writerBuilder, carData);
      
        std::cout<<"AVjsonString="<<jsonString<<std::endl;
        
        // 发布JSON数据到通道
        std::string l_topic = m_taskStatus.fusionResult;"beijingdata";
        reply = (redisReply*)redisCommand(context, "PUBLISH %s %s",l_topic.c_str(), jsonString.c_str());
        if (reply == NULL) {
            std::cout << "PUBLISH GKQResult 发布消息失败" << jsonString << std::endl;
        } else {
            std::cout << "发送 4.2主车实时信息--成功发布消息，已发送给 "<<l_topic<< "    " << reply->integer << " 个订阅者" << jsonString<<std::endl;
            freeReplyObject(reply);
        }

        if(!m_taskStatus.taskStatus)
        {
            ROS_INFO("task is over");
            return;
        }
        rate.sleep();

        frameCount++;
        if(frameCount>1000) frameCount=0;    
           
           
    }
}

void Redis_av::SubCallback_gps(const common_msgs::sensorgps::ConstPtr &msg)
{
    // std::cout<<"1111111"<<std::endl;
    std::unique_lock<std::mutex> lk(m_currentGpsMutex);
    m_currentGps= *msg;
    // std::cout<<"1111112"<<std::endl;
}

void Redis_av::SubCallback_actuator(const common_msgs::actuator::ConstPtr &msg)
{  
    // std::cout<<"1111113"<<std::endl;
    std::unique_lock<std::mutex> lk(m_currentActuatorMutex);
     m_actuatormsg = *msg;

    lk.unlock();
    // std::cout<<"1111141"<<std::endl;
}



void Redis_av::SubCallback_decisionbehavior(const common_msgs::decisionbehavior::ConstPtr &msg)
{
    m_decisionbehavior = *msg; 
}

void Redis_av::SubCallback_controllon(const common_msgs::controlcmd::ConstPtr &msg)
{
    std::unique_lock<std::mutex> lk(m_currentControllonMutex);
    m_currentControllon = *msg; 
    common_msgs::controllat l_lat;
    common_msgs::controllon l_lon;
    l_lon.timestamp = ros::Time::now().toSec()*1000;
    l_lon.gear = 4;//msg->gear;
    l_lon.gasPedal = msg->acceleration;
    pub_controllonWj.publish(l_lon);
    l_lat.epsangle = msg->wheel_angle * 12.09;
    l_lat.timestamp = ros::Time::now().toSec()*1000;
    pub_controllatWj.publish(l_lat);
}

void Redis_av::SubCallback_wanjiObject(const common_msgs::sensorobjects::ConstPtr &msg)
{
    m_sensorobjects = *msg;
}

void Redis_av::SubCallback_taskstatus(const device_msgs::taskstatus::ConstPtr &msg)
{
    ROS_INFO("recive task status");
    if(m_taskStatus.taskStatus != msg->taskStatus)
    {
        if(!msg->taskStatus)
        {
            m_taskStatus.taskStatus = msg->taskStatus;
        }
        if(msg->taskStatus)
        {
            m_taskStatus = *msg;
            //创建仿真车辆下发ros线程
            auto l_tmp = std::thread(&Redis_av::AVToCloudTH, this);
	        l_tmp.detach();

        }
       
        
    }
    
}


