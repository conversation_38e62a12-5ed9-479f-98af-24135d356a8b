#include "common.h"
void LocalXYToLatLon(const Point3D& car_p, Point3D& xypoint)
{
    double sin_lat1 = sin(car_p.lat * M_PI / 180.0);        // sin of start lat
    double cos_lat1 = cos(car_p.lat * M_PI / 180.0);    // sec of start lat
    double square_e = e_1 * e_1;
    double square_sin_lat1 = sin_lat1 * sin_lat1;
    double R_n = R_e / (sqrt(1 - square_e * square_sin_lat1));                  // 卯酉面等效曲率半径 (lon1, lat1)
    double R_m = (R_n * (1 - square_e)) / (1 - square_e * square_sin_lat1);     // 子午面等效曲率半径 (lon1, lat1)
    double temp_angle = car_p.azimuth * M_PI / 180;      // 自车航向角

    double x_enu = xypoint.x * cos(temp_angle) + xypoint.y * sin(temp_angle);
    double y_enu = -xypoint.x * sin(temp_angle) +  xypoint.y * cos(temp_angle);

    double lon_point = x_enu / (R_n * cos_lat1) * 180.0 / M_PI + car_p.lon;
    double lat_point = y_enu / R_m * 180.0 / M_PI + car_p.lat;

    xypoint.lon = lon_point;
    xypoint.lat = lat_point;
}