ps -ef | grep msgmiddleware.launch | grep -v grep | awk '{print $2}' | xargs kill -9


sleep 0.5


ps -ef | grep redis_av.launch | grep -v grep | awk '{print $2}' | xargs kill -9



gnome-terminal -x bash -c "cd  /home/<USER>/Downloads/20240715/20240716/newmsgs/tongji;source   /home/<USER>/Downloads/20240715/20240716/newmsgs/tongji/devel/setup.bash;roslaunch msgmiddleware msgmiddleware.launch;exec bash"
sleep 0.5

gnome-terminal -x bash -c "cd /home/<USER>/Downloads/20240715/20240716/newmsgs/tongji;source  /home/<USER>/Downloads/20240715/20240716/newmsgs/tongji/devel/setup.bash;roslaunch redis_av redis_av.launch;exec bash"
