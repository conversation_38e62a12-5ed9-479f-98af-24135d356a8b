#!/usr/bin/env python
PACKAGE = "recordbag"

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

gen.add("pre_abnormal_time",    int_t,    5, "pre abnormal time", -500,  0, 500)
gen.add("before_abnormal_time",    int_t,    5, "before abnormal time",   -500,  0, 500)
gen.add("actuator",   bool_t,   0, "A Boolean parameter",  True)
gen.add("behaviordecision",   bool_t,   0, "A Boolean parameter",  True)
gen.add("sensorgps",   bool_t,   0, "A Boolean parameter",  True)
gen.add("v2ifusion_fusionObject",   bool_t,   0, "A Boolean parameter",  True)

exit(gen.generate(PACKAGE, "recordbag", "paraminfo"))
