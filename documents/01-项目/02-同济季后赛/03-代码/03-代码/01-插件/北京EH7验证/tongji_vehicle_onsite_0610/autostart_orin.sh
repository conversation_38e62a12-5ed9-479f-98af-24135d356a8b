#! /bin/bash
current_directory=$(pwd)
echo "current: ${current_directory}"
sleep 2
gnome-terminal --tab -- bash -c "roscore; exec bash"
sleep 2
source ${current_directory}/devel/setup.bash
gnome-terminal --tab -- bash -c "roslaunch redisInfo redisInfo.launch; exec bash"
sleep 2
source ${current_directory}/devel/setup.bash
gnome-terminal --tab -- bash -c "roslaunch redis_av redis_av.launch; exec bash"
sleep 2
source ${current_directory}/devel/setup.bash
gnome-terminal --tab -- bash -c "roslaunch redis_sv redis_sv.launch; exec bash"
sleep 2
source ${current_directory}/devel/setup.bash
gnome-terminal --tab -- bash -c "roslaunch msgmiddleware msgmiddleware.launch; exec bash"

