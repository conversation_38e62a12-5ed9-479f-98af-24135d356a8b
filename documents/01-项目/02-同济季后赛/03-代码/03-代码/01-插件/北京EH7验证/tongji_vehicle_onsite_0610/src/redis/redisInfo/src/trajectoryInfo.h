#pragma once
#include <ros/ros.h>
#include <jsoncpp/json/json.h>
#include <hiredis/hiredis.h>
#include <iostream>
#include <fstream>
#include"device_msgs/participantTrajectories.h"
#include "device_msgs/taskpublish.h"
#include "device_msgs/startTask.h"
#include <math.h>
class trajectoryInfo
{
private:
    std::string m_ip;
    int m_port;
    bool m_gateway;
    std::string m_deviceId;
    device_msgs::participantTrajectories m_trajectory;
 
private:
    void sendSimuTrajectory(Json::Value p_json, ros::Publisher p_pub);
    bool sendToRedis(Json::Value p_json, std::string p_channel);
public:
    trajectoryInfo(std::string p_ip, int p_port, bool p_gateWay);
    ~trajectoryInfo();
    void setImageID(std::string ID);
    void parseTaskTrajectory(Json::Value p_json, ros::Publisher p_pub);
    void pasreSimuTrajectory(Json::Value p_json, ros::Publisher p_pub);
    void pasreTaskTest(Json::Value p_json, ros::Publisher p_pub);
    void startControl(bool p_start);
    device_msgs::participantTrajectories  testScene(Json::Value p_json, ros::Publisher p_pub);
    void testSceneWj(Json::Value p_json);
    void parseTaskStatus(Json::Value p_json, ros::Publisher p_pub);
    void taskResultToCloud(int p_statu);
};


