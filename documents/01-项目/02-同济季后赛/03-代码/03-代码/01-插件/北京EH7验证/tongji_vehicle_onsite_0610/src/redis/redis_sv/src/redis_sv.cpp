#include "redis_sv.h"
#include <hiredis/hiredis.h>
#include <jsoncpp/json/json.h>
#include <iostream>

#include <cmath>
redisContext* redis;
Json::Value statejson;
char zone[] = {0,0,0,0};
Redis_sv::Redis_sv(ros::NodeHandle nh)
{
    double lon = 108.89680433912669;
    double lat = 34.373651360108965;
    double  x, y;

    gps_common::LLtoUTM(lat, lon,y, x, zone);
    std::string node_name = ros::this_node::getName();
    std::cout << "Redis_sv constructor." << std::endl;
    ros::param::get("redis_ip",redis_ip);
    ros::param::get("redis_port",redis_port);
    nh.param(node_name+"/subname",m_subname,m_subname);
    nh.param(node_name+"/gateway",m_gateway,m_gateway);
    ros::param::get("online",m_online);


    

    sub_wanjigps = nh.subscribe("/sensorgps", 2, &Redis_sv::SubCallback_wanjigps, this);
    sub_taskStatus = nh.subscribe("/taskstatus", 2, &Redis_sv::SubCallback_taskstatus, this);

    pub_cloudpantsWj = nh.advertise<common_msgs::cloudpants>("/cloud/cloudobjects", 10);

    pub_sensorobjWj = nh.advertise<common_msgs::sensorobjects>("/objectTrack/track_results", 10);

    pub_stopline = nh.advertise<common_msgs::sensorobjects>("/platform_stoplines",10);
    
    long l_time =ros::Time::now().toSec()*1000;
    Json::Value objectArray;
    objectArray["timestamp"] = std::to_string(l_time);
    std::cout<<"l_time:"<<l_time<<"     "<<objectArray["timestamp"] <<std::endl;
    readObjectJson();
}

Redis_sv::~Redis_sv()
{
 
}

bool Redis_sv::init(void)
{ 
    return true;
}
long long frameid = 0;
// test online send obj to students


Json::Value stringToJson(const std::string& jsonString) {
    Json::Value jsonData;
    Json::Reader jsonReader;
 
    // 解析字符串并存入Json::Value对象
    bool parsingSuccessful = jsonReader.parse(jsonString, jsonData);
    if (!parsingSuccessful) {
        // 解析失败的处理
        std::cout << "Failed to parse JSON String" << std::endl;
        return Json::Value();
    }
 
    // 返回转换后的Json::Value对象
    return jsonData;
}


bool Redis_sv::SendsensorObjectWj(Json::Value dataArray)
{
    common_msgs::sensorobjects msg;
    msg.timestamp = ros::Time::now().toSec()*1000;
    for (const auto& item : dataArray["value"]) {
        common_msgs::sensorobject l_object;


        std::string l_id = item["id"].asString();
        l_object.id = stoi(l_id);

        l_object.relspeedy = item["speed"].asDouble() / 3.6 * cos(item["courseAngle"].asDouble() * M_PI / 180.0);
        std::cout<<"item[speed].asDouble()"<<item["speed"].asDouble()<<std::endl;
        l_object.relspeedx = item["speed"].asDouble() / 3.6 * sin(item["courseAngle"].asDouble() * M_PI / 180.0);
        l_object.azimuth = item["courseAngle"].asDouble() * M_PI / 180.0;
        
        l_object.longtitude = item["longitude"].asDouble();
        l_object.latitude = item["latitude"].asDouble();
        double l_reX, l_reY;
        m_wgs84_utm.LatLonToLocalXY(m_wanjigps.lon, m_wanjigps.lat, m_wanjigps.heading, l_object.longtitude,l_object.latitude, l_reX, l_reY );
        l_object.x = l_reX;
        l_object.y = l_reY;

        //障碍物尺寸

        l_object.height = item["height"].asFloat()/100.0;
        l_object.length = item["length"].asFloat()/100.0;
        l_object.width  = item["width"].asFloat()/100.0;

        //障碍物的类型
        l_object.classification = item["vehicleType"].asInt();

        msg.obs.push_back(l_object);
    }
    pub_sensorobjWj.publish(msg);
}


// xu ni object to guoqi
bool Redis_sv::SendcloudpantsWj(Json::Value dataArray)
{
    common_msgs::cloudpants msg;
    msg.timestamp = ros::Time::now().toSec()*1000;


    // 提取数据数组
    // std::cout<<"l_value[value].size():"<<l_value["value"].size()<<std::endl;
    for (const auto& item : dataArray["value"]) {


        common_msgs::cloudpant l_object;

            std::string l_id = item["id"].asString();
            l_object.Id = l_id.c_str();

            l_object.speed = item["speed"].asDouble() / 3.6;
            std::cout<<"item[speed].asDouble()"<<item["speed"].asDouble()<<std::endl;
            l_object.courseAngle = item["courseAngle"].asDouble() * M_PI / 180.0;
            std::cout<<" from cloud:"<<std::to_string(msg.timestamp)<<"    "<<l_id<<"   "<<item["courseAngle"].asDouble()<<"    "<<l_object.courseAngle<<std::endl;
            //位置
            l_object.longitude = item["longitude"].asDouble();
            l_object.latitude = item["latitude"].asDouble();
            

            //障碍物尺寸

            l_object.height = item["height"].asFloat()/100.0;
            l_object.length = item["length"].asFloat()/100.0;
            l_object.width  = item["width"].asFloat()/100.0;

            //障碍物的类型
            l_object.vehicletype = item["vehicleType"].asInt();

            msg.pants.push_back(l_object);
    }
    
    pub_cloudpantsWj.publish(msg);



}



void Redis_sv::sendObjectToRosTH()
{
   ROS_INFO("sendDataToCloudTH thread create success!");
    ros::Rate rate(10);
     // 连接到Redis服务器
     redisContext* context = redisConnect(redis_ip.c_str(), redis_port);
    if (context == nullptr || context->err) {
        if (context) {
            std::cerr << "Failed to connect to Redis: " << context->errstr << std::endl;
        } else {
            std::cerr << "Failed to allocate redis context." << std::endl;
        }
        //return 1;
    }
      // 验证密码
    redisReply* reply = nullptr;
     if(m_gateway)
     {
        const char* redis_pwd = "Wanji@300552!";
        reply = (redisReply*)redisCommand(context, "AUTH %s", redis_pwd);
        if (reply == nullptr || reply->type == REDIS_REPLY_ERROR) {
            std::cout << "Failed to authenticate with Redis server" << std::endl;
            freeReplyObject(reply);
            redisFree(context);
            // return -1;
        }
     }
    std::string l_cmd = m_taskStatus.simResult;
    ROS_INFO("cloud object SUBSCRIBE channel is %s",l_cmd.c_str());
    reply = (redisReply*)redisCommand(context, "SUBSCRIBE %s",l_cmd.c_str());//TESSResult m_subname
    
    // 订阅HMIControl频道
    if (reply == nullptr) {
        std::cerr << "Failed to subscribe to HMIControl channel." << std::endl;
        redisFree(context);
        //return 1;
    }
    freeReplyObject(reply);
    while (ros::ok())
    {
 
        // 开始监听消息
        while (redisGetReply(context, (void**)&reply) == REDIS_OK) {
            // std::cout<<"reply->elements:"<<(int)reply->elements<<std::endl;
           
            if (reply->type == REDIS_REPLY_ARRAY && reply->elements == 3) {
                std::string channel = reply->element[1]->str;
                std::string message = reply->element[2]->str;

                // 解析JSON数据
                Json::CharReaderBuilder reader;
                std::string errs;
                std::istringstream iss(message);
           
                if (!Json::parseFromStream(reader, iss, &statejson, &errs)) 
                {
                    std::cerr << "Failed to parse JSON: " << errs << std::endl;
                    continue;
                }
                std::cout<<statejson<<std::endl; 
                if(statejson.isNull())
                {
                    std::cout<<"parsedData: NULL!"<<std::endl;
                }
                else
                {

                  if(!statejson["value"].isNull())
                  {
                    if(m_online)
                    {
                        bool l_re =  SendcloudpantsWj(statejson["value"]);   // 实车发送的话题   发送云端障碍物cloudobject话题解析
                    }else{
                        bool l_re =  SendsensorObjectWj(statejson["value"]);   // 仿真发送的话题模拟车端 8conerRVIZ 解析
                    }
                  }else
                  {
                    std::cout<<"statejson[value] is null"<<std::endl;
                  }
                    
                }
            }
            
            freeReplyObject(reply);
            if(!m_taskStatus.taskStatus)
            {
                ROS_INFO("task is over");
                return;
            }
        }
        {
            std::cout<<"REDIS is not ok"<<std::endl;
        }

   

    }
}









void Redis_sv::SubCallback_wanjigps(const common_msgs::sensorgps::ConstPtr &msg)
{
    m_wanjigps = *msg;
}       
void Redis_sv::SubCallback_taskstatus(const device_msgs::taskstatus::ConstPtr &msg)
{
    ROS_INFO("recive task status");
    if(m_taskStatus.taskStatus != msg->taskStatus)
    {
        if(!msg->taskStatus)
        {
            m_taskStatus.taskStatus = msg->taskStatus;
        }
        if(msg->taskStatus)
        {
            m_taskStatus = *msg;
            //创建仿真车辆下发ros线程
            auto l_tmp = std::thread(&Redis_sv::sendObjectToRosTH, this);
	        l_tmp.detach();

        }
       
        
    }
    
}





void Redis_sv::printObjects(const json& objects) {

    for (const auto& object : objects) {
        int l_id = object["id"];
        if( l_id == 102)
        {
            common_msgs::sensorobject l_object;
            l_object.id = l_id;
            l_object.length = object["length"];
            l_object.width = object["width"];
            l_object.height = object["height"];
            l_object.azimuth = object["heading"];
            double x = object["center_absolute"]["x"];
            double y = object["center_absolute"]["y"];
            double lon, lat;
            gps_common::UTMtoLL(y, x, zone, lat, lon);
            l_object.latitude = lat;
            l_object.longtitude = lon;  
            m_objects.obs.push_back(l_object);
            break;
        }
    }
    m_objects.timestamp =ros::Time::now().toSec()*1000;
    //创建发送线程
    pub_stopline.publish(m_objects);
    auto objth = std::thread(&Redis_sv::objectThread, this);
    objth.detach();

}

void Redis_sv::objectThread()
{
    while (1)
    {
        //    publish
        m_objects.timestamp =ros::Time::now().toSec()*1000;
        pub_stopline.publish(m_objects);
        usleep(200*1000);
    }

}


void Redis_sv::readObjectJson()
{
 std::ifstream file("/home/<USER>/wanji_suv/road_elements.json");
    if (!file.is_open()) {
        std::cerr << "Failed to open JSON file" << std::endl;
        return ;
    }

    json j;
    file >> j; // Parse the JSON file

    if (j.is_object() && j.contains("objects") && j["objects"].is_array()) {
        printObjects(j["objects"]);
    } else {
        std::cerr << "Invalid JSON format" << std::endl;
        return ;
    }   
}
