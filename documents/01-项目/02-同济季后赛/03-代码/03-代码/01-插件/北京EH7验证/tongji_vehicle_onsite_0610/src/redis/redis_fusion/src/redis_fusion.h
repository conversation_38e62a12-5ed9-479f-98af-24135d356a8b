#include <ros/ros.h>
#include <jsoncpp/json/json.h>

#include "common_msgs/sensorgps.h"
#include "common_msgs/actuator.h"
#include "common_msgs/controllon.h"
#include "common_msgs/decisionbehavior.h"
#include "device_msgs/taskstatus.h"
#include "common_msgs/sensorobjects.h"
#include <thread>
#include <filesystem>
#include <condition_variable>
#include <iostream>
#include <fstream>
#include "log.h"
#include "conversions.h"

namespace fs = std::filesystem;



struct NetworkStats {
    unsigned long long rx_bytes;  // 接收字节数
    unsigned long long tx_bytes;  // 发送字节数
};

//发送自车消息到redis
class redis_fusion
{
private:
  void SubCallback_gps(const common_msgs::sensorgps::ConstPtr &msg);
  void SubCallback_actuator(const common_msgs::actuator::ConstPtr &msg);
  // void SubCallback_planningmotion(const common_msgs::planningmotion::ConstPtr& msg);

  // void SubCallback_controllon(const common_msgs::controllon::ConstPtr &msg);
  void SubCallback_taskstatus(const device_msgs::taskstatus::ConstPtr &msg);
  void SubCallback_vehiclefdb(const common_msgs::controllon::ConstPtr &msg);
  void SubCallback_object(const common_msgs::sensorobjects::ConstPtr &msg);
  void SubCallback_decisionbehavior(const common_msgs::decisionbehavior::ConstPtr &msg);
  void svframeLog(common_msgs::sensorobjects msg);

  Json::Value svframe(int p_frame);
  Json::Value avframe(int p_frame);
  std::string getTaskID(const std::string p_info);
  double pingResust(const std::string &p_ip);
  // void bandwidthResult(const std::string &p_net);
  NetworkStats readNetworkStats(const std::string& interface);



private: 
    ros::Subscriber sub_gps;
    ros::Subscriber sub_actuator;

    ros::Subscriber sub_obuStatus;
    ros::Subscriber sub_taskStatus;
    ros::Subscriber sub_vehiclefdb;
    ros::Subscriber sub_objects;
    common_msgs::sensorgps      m_currentGps;
    common_msgs::actuator       m_currentActuator;  
    common_msgs::controllon       m_currentControllon;
    common_msgs::decisionbehavior m_decisionbehavior;
    std::mutex m_currentGpsMutex;
    std::mutex m_currentActuatorMutex;
    std::mutex m_currentControllonMutex;
    std::mutex m_currentVehicleMutex;
    std::mutex m_currentObjectMutex;
    std::mutex m_currentdecisionbehaviorMutex;
public: 
    redis_fusion(ros::NodeHandle nh);
    ~redis_fusion();

    void AVToCloudTHMonitor();
    void fusionToCloudTH();
    void bandwidthResult();
    int count;
    std::string redis_ip;
    int redis_port;
    std::string m_deviceID;
    bool m_gateway = true;
    int m_carID = 0;
    double m_carLen = 0.0;
    std::mutex m_task_mutex;
    device_msgs::taskstatus m_taskStatus;
    std::shared_ptr<spdlog::logger> logger = NULL;

    std::mutex m_mtx;
    std::condition_variable m_cv;
    bool m_stopThread = false;
    
    std::string m_logfile;
    std::mutex m_object_mutex;
    common_msgs::sensorobjects m_objects;

    std::string m_serverIP;
    std::string m_cpeIP;
    std::string m_baseIP;
    std::string m_netNum;
    std::mutex m_brand_mutex;
    float m_networkUp = 0;
    float m_networkDown = 0;
    NetworkStats m_netStats;
    std::mutex m_net_mutex;
    double m_recvNet;
    double m_sendNet;
    //int m_deviceID ;
    std::ofstream m_livinglog;
};