#include "trajectoryInfo.h"
#include "device_msgs/simulationPublish.h"
trajectoryInfo::trajectoryInfo(std::string p_ip, int p_port, bool p_gateWay):m_ip(p_ip),m_port(p_port),m_gateway(p_gateWay)
{

}

trajectoryInfo::~trajectoryInfo()
{
}
void trajectoryInfo::setImageID(std::string ID)
{
    m_deviceId = ID;
}
// 解析测试任务，数据发送到选手
void trajectoryInfo::parseTaskTrajectory(Json::Value p_json, ros::Publisher p_pub)
{
    if(p_json["type"].isInt())
    {
        uint8_t l_type = p_json["type"].asInt();
        if(l_type == 0)//设备是否在线请求
        {

        }else if(l_type == 1) //设备准备状态
        {

        }else if(l_type == 2) //测试任务控制
        {
            if(p_json["params"]["taskType"].isInt())  
            {
                uint8_t l_cmd = p_json["params"]["taskType"].asInt();
                if(l_cmd == 1)//任务开始
                {
                    //仿真数据下发
                }else if(l_cmd == 0)   //任务结束
                {

                }
            }
        }else if(l_type == 3) //行为控制数据
        {

        }else if(l_type == 4) //镜像列表
        {

        }
    }

}


// 解析仿真轨迹，并将数据保存
void trajectoryInfo::pasreSimuTrajectory(Json::Value p_json, ros::Publisher p_pub)
{
    if(p_json["type"].isString())
    {
        std::string l_cmd = p_json["type"].asString();
        if(l_cmd == "start")  //开始   和 任务开始的作用区别
        {

        }else if(l_cmd == "trajectory") //轨迹
        {
            sendSimuTrajectory(p_json["value"],p_pub);
        }else if(l_cmd == "end") //结束
        {

        }
    }
}


void trajectoryInfo::pasreTaskTest(Json::Value p_json, ros::Publisher p_pub)
{
    device_msgs::participantTrajectories l_trajectory;

}

// 发送仿真轨迹信息
void trajectoryInfo::sendSimuTrajectory(Json::Value p_json, ros::Publisher p_pub)
{
    device_msgs::simulationPublish l_path;
    l_path.timestamp = std::atoi(p_json["timestamp"].asString().c_str());


        Json::Value dataArray = p_json["value"];//parsedData["value"];

        for (const auto& item : dataArray) 
        {
            device_msgs::trajectory l_trajectory;
            l_trajectory.globalTimeStamp = item["globalTimeStamp"].asString();
            l_trajectory.timestamp = item["timestamp"].asString();
            l_trajectory.frameId = item["frameId"].asInt();
            l_trajectory.id = item["id"].asString();
            l_trajectory.name = item["name"].asString();
            l_trajectory.picLicense = item["picLicense"].asString();
            l_trajectory.originalColor = item["originalColor"].asInt();
            l_trajectory.vehicleColor = item["vehicleColor"].asInt();
            l_trajectory.vehicletype = item["vehicleType"].asInt();
            l_trajectory.length = item["length"].asInt();
            l_trajectory.width = item["width"].asInt();
            l_trajectory.height = item["height"].asInt();
            l_trajectory.driveType = item["driveType"].asInt();
            l_trajectory.latitude = item["latitude"].asDouble();
            l_trajectory.longitude = item["longitude"].asDouble();
             l_trajectory.courseAngle = item["courseAngle"].asDouble();
             l_trajectory.speed = item["speed"].asInt();
             l_trajectory.lonAcc = item["lonAcc"].asDouble();
             l_trajectory.latAcc = item["latAcc"].asDouble();
             l_trajectory.angularVelX = item["angularVelocityX"].asDouble();
             l_trajectory.acceleratorPedal = item["acceleratorPedal"].asDouble();
             l_trajectory.braking = item["braking"].asDouble();
             l_trajectory.obuStatus =item["obuStatus"].asInt();
             l_trajectory.locationStatus = item["locationStatus"].asInt();
             l_trajectory.chassisStatus = item["chassisStatus"].asInt();
             l_trajectory.autoStatus =item["autoStatus"].asInt();
             l_trajectory.indicatorStatus = item["indicatorStatus"].asInt();
             l_trajectory.chassisStatus = item["blinkerStatus"].asInt();
             l_path.value.push_back(l_trajectory);
        }
    p_pub.publish(l_path);

}


// 根据指令下发 仿真数据至选手
void trajectoryInfo::startControl(bool p_start)
{

}

// 测试场景下发
device_msgs::participantTrajectories trajectoryInfo::testScene(Json::Value p_json, ros::Publisher p_pub)
{
    device_msgs::participantTrajectories l_trajectory;

    // std::string l_imageID =  p_json["imageId"].asString();
    Json::Value dataArray = p_json["params"]["participantTrajectories"];//parsedData["value"];
    std::cout<<"dataArray.size():"<<dataArray.size()<<std::endl;
    // device_msgs::participantTrajectories l_trajectory;
    if(dataArray.size() <= 0)
    {
        return l_trajectory;
    }

    for (const auto& item : dataArray) 
    {
        device_msgs::posArray l_pos;
        l_pos.longitude =  item["value"][0]["longitude"].asDouble();
        l_pos.latitude = item["value"][0]["latitude"].asDouble();


        double courseAngle =item["value"][0]["courseAngle"].asFloat();//l_currentGps.yaw;  //航向角的变换

        //由国汽 0--east, 90 -- north 角度改为 正北夹角，因此不需要转换
        //if((90 - courseAngle) < 0)
        //{
        //   l_pos.courseAngle =(90 - courseAngle) + 360;
        //}else{
        //    l_pos.courseAngle =(90 - courseAngle);
        //}
        l_pos.courseAngle = courseAngle;
        std::cout<<"l_pos.courseAngle:"<<l_pos.courseAngle<<std::endl;
        l_pos.speed = item["value"][0]["speed"].asFloat();
        l_pos.siteType = item["value"][0]["siteType"].asInt64();
        l_trajectory.value.push_back(l_pos);
    }
    ROS_INFO("have pub");

    //下发状态上报
    taskResultToCloud(1);
    return l_trajectory;
}


//需要重启规控算法
void trajectoryInfo::testSceneWj(Json::Value p_json)
{
    // std::cout<<p_json<<std::endl;
    if( p_json["params"]["participantTrajectories"].isNull())
    {
        return;
    }
    std::string l_imageID =  p_json["params"]["imageId"].asString();
    Json::Value dataArray = p_json["params"]["participantTrajectories"] ;//parsedData["value"];
    device_msgs::participantTrajectories l_trajectory;
    if(dataArray.size() <= 0)
    {
        return;
    }
    
    //创建 地图文件
    std::ofstream outFile("/home/<USER>/Vanjeemap/mapfiles/yuanqu1/maping1.txt");
    if(!outFile)
    {
        std::cerr <<"can not open txt!" <<std::endl;
        return;
    }
    int i = 0;
    double start_lon = 0;
    double start_lat = 0;
    double start_heading = 0;
    double init_speed = 0;
    double init_yawrate = 0;
    double start_steeringWheelAngle = 0;
    double start_accPedal = 0;
    double start_dccPedal = 0;
    for (const auto& item : dataArray) 
    {
        // std::cout<<item<<std::endl;

        if(i==0)  //设置起始点
        {
             start_lon =  item["value"][0]["longitude"].asDouble();
            // std::cout<<"start_lon="<<start_lon<<std::endl;
             start_lat = item["value"][0]["latitude"].asDouble();
             start_heading = item["value"][0]["courseAngle"].asDouble();
             init_speed = item["value"][0]["speed"].asDouble();
            ros::param::set("AD_START",false);
    
            ros::param::set("car/start_lat",start_lat);
            ros::param::set("car/start_lon",start_lon);
            ros::param::set("car/start_heading",start_heading);
            ros::param::set("car/init_speed",(float)(init_speed/3.6));
            ros::param::set("car/init_yawrate","0");
            ros::param::set("car/start_steeringWheelAngle",start_steeringWheelAngle);
            ros::param::set("car/start_accPedal",start_accPedal);
            ros::param::set("car/start_dccPedal",start_dccPedal);
            ros::param::set("AD_START",true);
        }
        i++;
        double longitude = item["value"][0]["longitude"].asDouble();
 
        double latitude = item["value"][0]["latitude"].asDouble();
        // std::cout<<item["timestamp"].asDouble()<<"    "<<latitude<<std::endl;
        int roadtype = 1;
        double speed = item["value"][0]["speed"].asFloat();
        int lanetype = 3;
        int mergetype = 1;
        int roadstatus = 1;
        int turnlight = 1;
        int rightstopdis = 1;
        double nowlanewidth = 3.5;
        double leftlanewidth = 3.5;
        double rightlanewidth = 3.5;
        double leftsafedis = 1.75;
        double rightsafedis = 1.75;                   
        double courseAngle =item["value"][0]["courseAngle"].asFloat();
        double gpstime = 6;   
        double starnum = 6;   

        outFile << std::fixed << std::setprecision(12);
        outFile << longitude <<"," << latitude <<  ","<< roadtype << "," 
        << speed <<"," << lanetype <<  ","<< mergetype << "," 
        << roadstatus <<"," << turnlight <<  ","<< rightstopdis << "," 
        << nowlanewidth <<"," << leftlanewidth <<  ","<< rightlanewidth << "," 
        << leftsafedis <<"," << rightsafedis <<  ","<< courseAngle << "," 
        <<gpstime <<"," << starnum <<  "," << std::endl;
    }
    outFile.close();

    //重启域控算法
    std::cout <<"txt writing is finish" <<std::endl;
    const char* scriptPath = "/home/<USER>/jihousai/container1/pingtai_redis/docker0/restart.sh";
    int restartresult =0;//= std::system(scriptPath);
    if(restartresult == 0)
    {
        std::cout<<"重启决策规划算法，测试用例切换完成！"<<std::endl;



    
            ros::param::set("car/start_lat",start_lat);
            ros::param::set("car/start_lon",start_lon);
            ros::param::set("car/start_heading",start_heading);
            ros::param::set("car/init_speed",(float)(init_speed/3.6));
            ros::param::set("car/init_yawrate","0");
            ros::param::set("car/start_steeringWheelAngle",start_steeringWheelAngle);
            ros::param::set("car/start_accPedal",start_accPedal);
            ros::param::set("car/start_dccPedal",start_dccPedal);
            ros::param::set("AD_START",true);
        // ros::param::set("AD_START",false);

    }
    else
    {
    std::cout<<"未重启决策规划算法，测试用例切换失败！"<<std::endl; 
    }  
    taskResultToCloud(1);
}

void trajectoryInfo::taskResultToCloud(int p_status)
{
    Json::Value l_data;
    long l_time =ros::Time::now().toNSec();
    l_data["timestamp"] = std::to_string(l_time);
    l_data["status"] = p_status;
    l_data["deviceId"] = m_deviceId;

    std::string l_channle = "CDJHS_TestIssueResult";
    bool l_re = sendToRedis(l_data, l_channle);
    if(!l_re)
    {
        ROS_INFO("task result send cloud failed");
    }else
    {
        ROS_INFO("task result send cloud sucess ");
    }
}


//根据平台下发的任务状态指挥选手 是否开始或者结束任务
void trajectoryInfo::parseTaskStatus(Json::Value p_json, ros::Publisher p_pub)
{
    device_msgs::startTask l_task;
    l_task.timestamp = p_json["timestamp"].asDouble();
    l_task.taskType = p_json["params"]["taskType"].asInt();
    p_pub.publish(l_task);

    
}

bool trajectoryInfo::sendToRedis(Json::Value p_json, std::string p_channel)
{
    redisContext* context = redisConnect(m_ip.c_str(), m_port);
    if (context == nullptr || context->err) {
        if (context) {
            std::cerr << "Failed to connect to Redis: " << context->errstr << std::endl;
        } else {
            std::cerr << "Failed to allocate redis context." << std::endl;
            return false;
        }
        
    }
    redisReply* reply = nullptr;
    if(m_gateway)
    {
        const char* redis_pwd = "Wanji@300552!";
        reply = (redisReply*)redisCommand(context, "AUTH %s", redis_pwd);
        if (reply == nullptr || reply->type == REDIS_REPLY_ERROR) {
            std::cout << "Failed to authenticate with Redis server" << std::endl;
            freeReplyObject(reply);
            redisFree(context);
            return false;
        } 

    }
    Json::StreamWriterBuilder writerBuilder;
    std::string jsonString = Json::writeString(writerBuilder, p_json);
    
    reply = (redisReply*)redisCommand(context, "PUBLISH %s %s", p_channel.c_str(),jsonString.c_str());
    // reply = (redisReply*)redisCommand(context, "PUBLISH CA001MatchResultData %s", jsonString.c_str());

    if (reply == NULL) {
        std::cout << "PUBLISH GKQResult 发布消息失败" << p_json << std::endl;
        return false;
    } else {
        std::cout << p_channel<<" topic 成功发布消息，已发送给 " << reply->integer << " 个订阅者" <<std::endl;
        freeReplyObject(reply);
    }
    return true;
}