<launch>
<param name = "deviceId" type = "string" value = "1130"/>
   <node name="redis_fusion"     pkg="redis_fusion"     type="redis_fusion"  output="screen"  respawn="true"> 
      <param name = "serverIP" type= "string" value = "127.0.0.1"/>
      <param name = "cpeIP" type= "string" value = "127.0.0.1"/>
      <param name = "baseIP" type= "string" value = "127.0.0.1"/>
      <param name = "net" type= "string" value = "eqos"/>
   </node>
</launch>
