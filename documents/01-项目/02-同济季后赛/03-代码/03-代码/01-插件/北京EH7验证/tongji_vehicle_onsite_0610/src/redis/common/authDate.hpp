#include <iostream>
#include <fstream>
#include <ctime>
#include <cstring>

// 模拟简单的加密函数
std::string simpleEncrypt(const std::string& data, int key) {
    std::string encrypted = data;
    for (char& c : encrypted) {
        c ^= static_cast<char>(key);
    }
    return encrypted;
}

bool isExpired() {
    const std::string file_path = "expiration.dat";
    const int encryption_key = 42; // 简单的加密密钥

    // 设定到期日期: 2025年8月1日
    struct tm expiration_date = {};
    expiration_date.tm_year = 2025 - 1900;
    expiration_date.tm_mon = 7;
    expiration_date.tm_mday = 12;

    std::time_t expire_time = std::mktime(&expiration_date);

    // 尝试读取已存储的过期时间
    std::ifstream infile(file_path, std::ios::binary);
    if (infile.is_open()) {
        std::string encrypted_expire_time_str;
        std::getline(infile, encrypted_expire_time_str);
        infile.close();

        // 解密
        std::string expire_time_str = simpleEncrypt(encrypted_expire_time_str, encryption_key);

        // 转换为 time_t
        std::tm tm_expire = {};
        strptime(expire_time_str.c_str(), "%Y-%m-%d %H:%M:%S", &tm_expire);
        expire_time = std::mktime(&tm_expire);
    } else {
        // 如果文件不存在，则创建并写入加密后的过期时间
        std::ofstream outfile(file_path, std::ios::binary);
        if (!outfile.is_open()) {
            std::cerr << "Failed to create expiration file.\n";
            return true;
        }

        char buffer[20];
        strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", std::localtime(&expire_time));
        std::string expire_time_str(buffer);
        std::string encrypted_expire_time_str = simpleEncrypt(expire_time_str, encryption_key);
        outfile << encrypted_expire_time_str;
        outfile.close();
    }

    std::time_t current_time = std::time(nullptr);
    return current_time > expire_time;
}

//int main() {
//    if (isExpired()) {
//        std::cerr << "This program has expired and can no longer be used.\n";
//        return 1;
//    }
//
//    std::cout << "Program is running...\n";
//    // 这里放置你的程序逻辑
//    return 0;
//}