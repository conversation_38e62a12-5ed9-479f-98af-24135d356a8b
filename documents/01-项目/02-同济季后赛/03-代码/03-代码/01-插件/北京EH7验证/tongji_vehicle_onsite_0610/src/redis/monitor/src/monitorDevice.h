#pragma once
#include <ros/ros.h>
#include <jsoncpp/json/json.h>
#include "common_msgs/sensorgps.h"
#include "common_msgs/actuator.h"
#include "common_msgs/sensorstatus.h"
#include "common_msgs/planningmotion.h"
#include "common_msgs/controllon.h"

#include "device_msgs/taskstatus.h"


#include <thread>
#include <filesystem>
#include <condition_variable>
#include <iostream>
#include <fstream>
#include <sys/statvfs.h>
#include "log.h"
#include "conversions.h"
namespace fs = std::filesystem;

struct topicHZ_t{
    int gps;
    int object;
    int simulat;
    int contronCmd;
    double gpsTime;
    double objectTime;
    double simulatTime;
    double controlCmdTime;
    topicHZ_t()
    {
        this->contronCmd = 0;
        this->gps = 0;
        this->simulat = 0;
        this->object = 0;
        this->gpsTime = 0;
        this->objectTime = 0;
        this->simulatTime = 0;
        this->controlCmdTime = 0;
    }
};

struct NetworkStats {
    unsigned long long rx_bytes;  // 接收字节数
    unsigned long long tx_bytes;  // 发送字节数
};

struct CpuStat {
    unsigned long long user;
    unsigned long long nice;
    unsigned long long system;
    unsigned long long idle;
    unsigned long long iowait;
    unsigned long long irq;
    unsigned long long softirq;
    unsigned long long steal;
    unsigned long long guest;
    unsigned long long guest_nice;
};





class monitorDevice
{
private:
    std::string redis_ip;
    int redis_port;
    std::string m_deviceID;
    ros::Subscriber sub_gps;
    ros::Subscriber sub_actuator;
    ros::Subscriber sub_obuStatus;
    ros::Subscriber sub_taskStatus;
    ros::Subscriber sub_vehiclefdb;
    ros::Subscriber sub_objects;
    topicHZ_t m_topicRecord;
    device_msgs::taskstatus m_taskStatus;
    bool m_gateway = false;
    int m_carID = 0;
    std::string m_teamName;
    NetworkStats m_netStats;
    std::string m_netNum;
    std::string m_serverIP;
    std::string m_cpeIP;
    std::string m_baseIP;
    common_msgs::sensorgps m_gps;
    std::vector<int> m_pidVec;

    common_msgs::actuator m_actuator;
public:
    monitorDevice(ros::NodeHandle nh);
    ~monitorDevice();
    void SubCallback_gps(const common_msgs::sensorgps::ConstPtr &msg);
    void SubCallback_actuator(const common_msgs::actuator::ConstPtr &msg);
    void SubCallback_taskstatus(const device_msgs::taskstatus::ConstPtr &msg);
    void SubCallback_object(const common_msgs::sensorgps::ConstPtr &msg);

    std::string getTaskID(const std::string p_info);
    double pingResust(const std::string &p_ip);
    // void bandwidthResult(const std::string &p_net);
    NetworkStats readNetworkStats(const std::string& interface);
    void bandwidthResult();
    CpuStat readCpuStat();
    std::unordered_map<std::string, unsigned long long> readMemInfo() ;
private:
    void recvRedis();
    void sendRedis();
    double monitorCPU(CpuStat &prev_stat, CpuStat &curr_stat);
    
    std::string monitorMemory();
    float monitorDisk();
    bool is_process_running(int pid); //检查进程是否存在
    bool getGPSStatus();//查看定位信息
    double getCpuUsage(int pid);
    long getMemoryUsage(int pid);
    void getDiskUsage(int pid, long& readBytes, long& writeBytes);
    void getPid(std::string p_string);
};



