#include "log.h"

std::shared_ptr<spdlog::logger> logger = NULL;
bool is_save_log_file = true;
bool is_console_log_enable = false;

void InitLogger(std::string filename) {

  if (spdlog::get("console") != nullptr) {
    logger = spdlog::get("");
    return;
  }

  std::vector<spdlog::sink_ptr> sinkList;
  if (is_console_log_enable) {
    auto consoleSink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    consoleSink->set_level(spdlog::level::debug);
    consoleSink->set_pattern("%v");
    sinkList.push_back(consoleSink);
  }

  if (is_save_log_file) {
    auto rotateSink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(filename, 1024 * 1024 * 1024 * 5, 2);
    rotateSink->set_level(spdlog::level::debug);
    rotateSink->set_pattern("%v");
    sinkList.push_back(rotateSink);
  }

  logger = std::make_shared<spdlog::logger>("console", begin(sinkList), end(sinkList));
  spdlog::register_logger(logger);
  logger->set_level(spdlog::level::debug);
  logger->flush_on(spdlog::level::debug);
}

void UninitLogger() {
  spdlog::drop_all();
}