#include "redis_sv.h"
#include <iostream>
#include <cstdio>
#include <array>

void test()
{
    std::string l_str = "{\"timestampType\": \"CREATE_TIME\", \"value\": [{\"timestamp\": \"2024-07-06 16:05:05.443\", \"globalTimeStamp\": 1720253105443, \"frameId\": 76, \"name\": \"200032\", \"id\": \"1\", \"picLicense\": \"china\", \"originalColor\": 3, \"vehicleColor\": 0, \"vehicleType\": 1, \"length\": 449, \"width\": 195, \"height\": 131, \"driveType\": 2, \"longitude\": 108.89467963553908, \"latitude\": 34.3729310434537, \"courseAngle\": 67.5791494521737, \"speed\": 1.9800000000000002, \"size_length\": 4.5, \"size_width\": 2.0, \"size_height\": 1.7, \"subsType\": 3, \"position_x\": 306416.05832200043, \"position_y\": 3805515.603281839, \"position_z\": 0, \"vxabs\": -0.5084240132425955, \"vyabs\": 0.2097737418226913, \"polygon_points\": [{\"polygon_point_x\": 306417.80936246295, \"polygon_point_y\": 3805513.**********}, {\"polygon_point_x\": 306415.**********, \"polygon_point_y\": 3805513.**********}, {\"polygon_point_x\": 306414.30728219217, \"polygon_point_y\": 3805517.**********}, {\"polygon_point_x\": 306416.**********, \"polygon_point_y\": 3805518.056967572}], \"polygon_point_length\": 4}, {\"timestamp\": \"2024-07-06 16:05:05.443\", \"globalTimeStamp\": 1720253105443, \"frameId\": 76, \"name\": \"200031\", \"id\": \"2\", \"picLicense\": \"china\", \"originalColor\": 3, \"vehicleColor\": 0, \"vehicleType\": 1, \"length\": 449, \"width\": 195, \"height\": 131, \"driveType\": 2, \"longitude\": 108.89513996477656, \"latitude\": 34.373088472914645, \"courseAngle\": 67.62038406430011, \"speed\": 1.973874408841127, \"size_length\": 4.5, \"size_width\": 2.0, \"size_height\": 1.7, \"subsType\": 3, \"position_x\": 306458.75481131533, \"position_y\": 3805532.185579916, \"position_z\": 0, \"vxabs\": -0.5070014567223355, \"vyabs\": 0.20875993330791512, \"polygon_points\": [{\"polygon_point_x\": 306460.50460727524, \"polygon_point_y\": 3805530.453072569}, {\"polygon_point_x\": 306458.63571736205, \"polygon_point_y\": 3805529.73197912}, {\"polygon_point_x\": 306457.**********, \"polygon_point_y\": 3805533.918087623}, {\"polygon_point_x\": 306458.87390526047, \"polygon_point_y\": 3805534.**********}], \"polygon_point_length\": 4}";
    Json::CharReaderBuilder reader;
    std::string errs;
    std::istringstream iss(l_str);
    Json::Value l_tmpTem;
    if (!Json::parseFromStream(reader, iss, &l_tmpTem, &errs)) 
    {
        std::cout<<"l_tmpTem:"<<l_tmpTem<<std::endl;
        double speed = l_tmpTem["value"][0]["speed"].asDouble();
        double courseAngle = l_tmpTem["courseAngle"].asDouble();
        std::cout<<"speed:"<<speed<<std::endl;
    }    
}
int main(int argc,char *argv[])
{
    // ROS_INFO("File:%s, Line:%s",__FILE__, __LINE__);
    //  test();
    #if 1
    ros::init(argc,argv,"redis_svdata");
    ros::NodeHandle nh;
    Redis_sv node(nh);
    if(node.init())
    {
        std::cerr << "Redis_sv init ok." << std::endl;
        ros::spin();
    }
    else
    {
        std::cerr << "Redis_sv init fail." << std::endl;
    }
    #endif
    return 0;
}