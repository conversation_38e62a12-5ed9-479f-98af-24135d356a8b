<launch>

   <node name="monitorDevice"     pkg="monitorDevice"     type="monitorDevice"  output="screen"  respawn="true"> 
      <param name = "serverIP" type= "string" value = "***********"/>
      <param name = "cpeIP" type= "string" value = "127.0.0.1"/>
      <param name = "baseIP" type= "string" value = "127.0.0.1"/>
      <param name = "net" type= "string" value = "eqos"/>
      <param name = "alPid" type= "string" value = "200505"/>
   </node>
</launch>
