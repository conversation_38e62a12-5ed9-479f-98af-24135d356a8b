#ifndef DYNAMICALMODEL_H_
#define DYNAMICALMODEL_H_
#include <iostream>
#include <typeinfo>
#include <cxxabi.h>
#include <cstring>
#include <stdio.h>
#include <fstream>
#include <vector>
#include <stdlib.h>
#include <deque>
#include <mutex>
#include <queue>
#include "ros/ros.h"
#include "ros/time.h"
#include "ros/package.h"
#include <rosbag/bag.h>
#include <rosbag/view.h>

#include "common_msgs/controlcmd.h"
#include "std_msgs/String.h"
#include "common_msgs/vehicleCB.h"
#include "common_msgs/sensorgps.h"
#include "device_msgs/participantTrajectories.h"
#include "wgs84_utm.h"
#include<cmath>
#include <algorithm>
#include <math.h>
#include <thread>
#include "conversions.h"
using namespace std;
#define WJ 1
#define CDJHS_DEF 0
class dynamicalModel
{
private:
    /* data */
    // pub sensorgps
    ros::Publisher pub_sensorgps;
    common_msgs::controlcmd m_contrlmsg;
    wgs84_utm m_wgs84_utm;
    common_msgs::sensorgps m_sensorgps;
    //sub cmd
    ros::Subscriber sub_actuator;
    ros::Subscriber sub_testsub;
    void SubCallback_controlcmd(const common_msgs::controlcmd::ConstPtr &msg);
    void SubCallback_participantTrajectories(const device_msgs::participantTrajectories::ConstPtr &msg);
    void rungps();
public:
    dynamicalModel(ros::NodeHandle& nh);
    ~dynamicalModel();
private:
    void modelFun(common_msgs::controlcmd p_cmd);
    double NormalizeAngle(const double angle);
    double m_lon;
    double m_lat;
    double m_yaw;
    double m_loop = 100;
    double m_wheel_base = 3.0;                              // 轴距
    double m_steer_ratio = 12.0;                            // 方向盘-前轮转角传动比
    double m_wheel_angle_max = 30.0;                        // 前轮转角最大值
    double m_wheel_angle_min = -30.0;                       // 前轮转角最小值
    double m_delta_wheel_angle_max = 15.0;                  // 每秒最大前轮转角变化量
    double m_acc_max = 2.0;                                 // 加速度最大值
    double m_acc_min = -4.0;                                // 加速度最小值
    double m_delta_acc_max = 5.0;                           // 每秒最大加速度变化量
    double m_speed_max = 60.0 / 3.6;                        // 车速最大值
    double m_predict_ts = 0.01;                             // 预测步长，单位s，可根据程序频率调整

    gps_common::Point2D m_start_point;
    gps_common::Point2D m_current_point;
    bool m_false = false;
};


#endif