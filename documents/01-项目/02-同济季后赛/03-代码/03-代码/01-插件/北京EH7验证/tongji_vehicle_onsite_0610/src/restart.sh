
ps -ef | grep msgmiddleware.launch | grep -v grep | awk '{print $2}' | xargs kill -9

# sleep 1

# cd /home/<USER>/ftd/pingtai_redis/docker0/workespace
# source /home/<USER>/ftd/pingtai_redis/docker0/workespace/devel/setup.bash

sleep 0.5
gnome-terminal -x bash -c "cd /home/<USER>/Downloads/20240715/20240716/tongji; source /home/<USER>/Downloads/20240715/20240716/tongji/devel/setup.bash;roslaunch msgmiddleware msgmiddleware.launch;exec bash"






