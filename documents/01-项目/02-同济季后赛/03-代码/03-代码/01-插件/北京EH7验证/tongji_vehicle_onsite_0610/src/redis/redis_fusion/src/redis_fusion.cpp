#include <ros/ros.h>
#include <iostream>
#include <string>
#include <hiredis/hiredis.h>
#include <jsoncpp/json/json.h>
#include"redis_fusion.h"
#include <fcntl.h>
#include <sstream>
#include <std_msgs/String.h>
#include <unistd.h>
redisContext* redis;
Json::Value statejson;

char zone[] = {0,0,0,0};
redis_fusion::redis_fusion(ros::NodeHandle nh)
{
    std::cout<<"构造函数"<<std::endl;
    std::string node_name = ros::this_node::getName();
    ros::param::get("redis_ip",redis_ip);
    ros::param::get("redis_port",redis_port);
    ros::param::get("gateway",m_gateway);
    ros::param::get("deviceId",m_deviceID);
    ros::param::get("carID",m_carID);
    ros::param::get("guoqiCarLen",m_carLen);
    ros::param::get("deviceId",m_deviceID);
    
    nh.param(node_name+"/serverIP",m_serverIP,m_serverIP);
    nh.param(node_name+"/cpeIP",m_cpeIP,m_cpeIP);
    nh.param(node_name+"/baseIP",m_baseIP,m_baseIP);
    nh.param(node_name+"/net",m_netNum,m_netNum);
    std::cout<<"m_deviceID:"<<m_deviceID<<std::endl;
    std::cout<<"redis_port:"<<redis_port<<std::endl;
    std::cout<<"guoqiCarLen:"<<m_carLen<<std::endl;
    sub_gps        = nh.subscribe("/sensorgps", 2, &redis_fusion::SubCallback_gps, this);
    sub_actuator   = nh.subscribe("/actuator", 2, &redis_fusion::SubCallback_actuator, this);
    sub_taskStatus = nh.subscribe("/taskstatus", 2, &redis_fusion::SubCallback_taskstatus, this);
    sub_vehiclefdb = nh.subscribe("/controllon", 2, &redis_fusion::SubCallback_vehiclefdb, this);
    sub_objects    = nh.subscribe("/objectTrack/track_results", 2, &redis_fusion::SubCallback_object, this);
    std::string logfile ; // 要创建的文件夹路径
    ros::param::get("logfile",m_logfile);
    try {
        if (fs::create_directory(m_logfile)) {
            std::cout << "Folder created successfully: " << m_logfile << std::endl;
        } else {
            std::cout << "Folder already exists or cannot be created: " << m_logfile << std::endl;
        }
    } catch (const fs::filesystem_error& e) {
        std::cerr << e.what() << std::endl;
    }



    auto l_tmp = std::thread(&redis_fusion::bandwidthResult, this);
    l_tmp.detach();

    char buffer[80];
    struct timeval tv; 
    gettimeofday(&tv,NULL);
    std::strftime(buffer, 80, "%Y-%m-%d %H", std::localtime(&tv.tv_sec));
    std::string mystamp(buffer);

    std::string logtpp = m_logfile +mystamp + "_monitor.log";
    std::cout<<"logtpp:"<<logtpp<<std::endl;
    m_livinglog.open(logtpp.c_str(),std::ios_base::app);
    // InitLogger(logtpp);
    // loggertpp= spdlog::get("console");
    if(!m_livinglog)
    {
        std::cout<<"**********************tpp txt open failed***********************"<<std::endl;
    }
    long l_time =ros::Time::now().toSec()*1000;
    std::string l_logstr = std::to_string(l_time) + ":  I am start work.....";
    m_livinglog<<l_logstr<<std::endl;

}
redis_fusion::~redis_fusion()
{
    std::cout<<"析构函数"<<std::endl;
    if(m_livinglog)
    {
        m_livinglog.close();
    }
    //   // 断开连接
    // redisFree(redis);
}
bool m_testFlag = false;
void redis_fusion::AVToCloudTHMonitor()
{
    auto l_tmp = std::thread(&redis_fusion::fusionToCloudTH, this);
    l_tmp.detach();
    while(1)
    {
        std::unique_lock<std::mutex> lck(m_mtx);
        m_cv.wait_for(lck, std::chrono::seconds(3),[]{return m_testFlag;});
        if(!m_testFlag)
        {
            std::cout<<" one times ---------------------"<<std::endl;
           

            usleep(100*1000);
            l_tmp = std::thread(&redis_fusion::fusionToCloudTH, this);
             l_tmp.detach();
            
        }
        
        m_testFlag = false;
        std::cout<<"=============================================:"<<m_testFlag<<std::endl;
        if(!m_taskStatus.taskStatus)
        {
            ROS_INFO("task is over");
            return;
        }
    }
}

//主车轨迹上报
void redis_fusion::fusionToCloudTH()
{
    ros::Rate rate(10);

    int frameCount = 0;
    // 连接到Redis服务器

    redisContext* context = redisConnect(redis_ip.c_str(), redis_port);
    if (context == nullptr || context->err) {
        if (context) {
            std::cerr << "Failed to connect to Redis: " << context->errstr << std::endl;
            return;
        } else {
            std::cerr << "Failed to allocate redis context." << std::endl;
        }
        //return 1;
    }
    redisReply* reply = nullptr;
    if(m_gateway)
    {
        const char* redis_pwd = "Wanji@300552!";
        reply = (redisReply*)redisCommand(context, "AUTH %s", redis_pwd);
        if (reply == nullptr || reply->type == REDIS_REPLY_ERROR) {
            std::cout << "Failed to authenticate with Redis server" << std::endl;
            freeReplyObject(reply);
            redisFree(context);
            // return -1;
        } 
    }
    long s_lastTime = ros::Time::now().toSec();
    std::string l_taskId = m_taskStatus.taskID;
    std::cout<<"taskID:"<<l_taskId<<std::endl;
    Json::FastWriter l_fastwriter ;
    while (ros::ok())
    {
 

        m_testFlag = true;

        Json::StreamWriterBuilder writerBuilder;
        Json::Value carData;
        carData["taskId"] = atoi(l_taskId.c_str()) ;
        carData["caseId"] = m_taskStatus.caseID;
        Json::Value l_sv = svframe(frameCount);
        if(l_sv["value"].size() >= 1)
        {
            carData["participantTrajectories"].append(l_sv);
        }
        Json::Value l_av = avframe(frameCount);
        
        carData["participantTrajectories"].append(l_av);

        
        std::string lineStr  = l_fastwriter.write(carData);

        //std::cout<<lineStr<<std::endl;
        // LOG_INFO(lineStr);
        // 发布JSON数据到通道
        std::string l_topic = m_taskStatus.gkqResult;//"CDJHS_GKQResult_"+ m_deviceID + "_fusion";// m_taskStatus.fusionResult;
        reply = (redisReply*)redisCommand(context, "PUBLISH %s %s",l_topic.c_str(), lineStr.c_str());
        if (reply == NULL) {
            std::cout << "PUBLISH GKQResult 发布消息失败" << lineStr << std::endl;
        } else {
            std::cout<< std::to_string(ros::Time::now().toSec())<< "   fusions send success "<<l_topic<< "    " << reply->integer << " 个订阅者" <<std::endl;
            freeReplyObject(reply);
        }
        carData["savelogTime"] = ros::Time::now().toSec()*1000;
        std::cout<<"carData[savelogTime]:"<<carData["savelogTime"]<<std::endl;
        // std::string jsonString = Json::writeString(writerBuilder, carData);
        std::string l_copyStr = l_fastwriter.write(carData);;
        size_t lastNonNewlinePos = l_copyStr.size();
        if(lastNonNewlinePos > 0)
        {
            l_copyStr = l_copyStr.erase(l_copyStr.size()-1);
        }
        logger->debug(l_copyStr);
        
        if(!m_taskStatus.taskStatus)
        {
            ROS_INFO("task is over");
            return;
        }
        rate.sleep();

        frameCount++;
        if(frameCount>1000) frameCount=0;    
           
           
    }
}


Json::Value redis_fusion::avframe(int p_frame)
{
    common_msgs::sensorgps   l_currentGps;
    common_msgs::actuator    l_currentActuator;
    common_msgs::controllon  l_currentControllon;
    common_msgs::decisionbehavior l_currentdecisionbehavior;
    {
        std::unique_lock<std::mutex> lk(m_currentGpsMutex);
        l_currentGps = m_currentGps;
        lk.unlock();
    }
    {
        std::unique_lock<std::mutex> lk(m_currentActuatorMutex);
        l_currentActuator = m_currentActuator;
        lk.unlock();
    }

    {
        std::unique_lock<std::mutex> lk(m_currentControllonMutex);
        l_currentControllon = m_currentControllon;
        lk.unlock();
    }
    {
        std::unique_lock<std::mutex> lk(m_currentdecisionbehaviorMutex);
        l_currentdecisionbehavior = m_decisionbehavior;
        lk.unlock();
    }
    // 创建一个空的JSON对象Json::Value jsonData;
    Json::Value carData;
    carData["type"] = "trajectory";

    Json::Value jsonData;

    char buffer[80];
    struct timeval tv; 
    gettimeofday(&tv,NULL);
    std::strftime(buffer, 80, "%Y-%m-%d %H:%M:%S", std::localtime(&tv.tv_sec));

    std::string mystamp(buffer);
    
    // jsonData["type"] = "trajectory";
    long l_time =ros::Time::now().toSec()*1000;
    jsonData["timestampType"] = "";
    jsonData["timestamp"] = std::to_string(l_time);
    jsonData["role"]="av";
    std::string l_source = "CDJHS_GKQResult_"+(m_deviceID);
    std::cout<<"l_source:"<<l_source<<std::endl;
    jsonData["source"]=l_source;
    // 添加一个string值taskTypemp;
        // 创建一个包含多个经度和纬度的JSON数组
    Json::Value jsonArray(Json::arrayValue);  
    Json::Value location(Json::objectValue);
    l_time =ros::Time::now().toSec()*1000;
    location["timestamp"] = std::to_string(l_time);;
    location["globalTimeStamp"] = std::to_string(l_time);
    location["frameId"] = p_frame;
    std::string id = "car1" ;
    // id = "1";
    location["id"] = m_deviceID;
    
    location["name"] = id;
    std::string picLicense = "";
    location["picLicense"] = picLicense;
    location["originalColor"] = 3;
    location["vehicleColor"] = 0;
    location["vehicleType"] = 1;
    location["length"] = 3.60;
    location["width"] = 1.60;
    location["height"] = 1.99;
    location["driveType"] = 1;
    switch(l_currentActuator.sysstatus) // 2 紧急制动
    {
        case 0: // 0 人工驾驶
            location["autoStatus"] = 0;
            // 当前驾驶模式（0：自动驾驶:1：人工驾驶:2：远程驾驶）
            break;
        case 1: // 1 自动驾驶
            location["autoStatus"] = 1;
    
            break;
        default: // 2 紧急制动
            location["autoStatus"] = 2;
            
            break;
    }

    double courseAngle = l_currentGps.heading;
    location["courseAngle"] = l_currentGps.heading;
//后轴中心改为车辆中心点   20241012
    double longitude = l_currentGps.lon;
    double latitude = l_currentGps.lat;

//后轴中心改为车辆中心点   20241012 

//    double l_y = cos(courseAngle * M_PI /180.0)*m_carLen + l_currentGps.position_y;
//
//    double l_X = sin(courseAngle * M_PI /180.0)*m_carLen + l_currentGps.position_x;

    location["longitude"] = longitude;
    location["latitude"] = latitude;
    location["speed"] = l_currentGps.velocity * 3.6;
    location["lonAcc"] =  l_currentGps.accx;
    location["latAcc"] = l_currentGps.accy * 9.8;

    location["angularVelocityX"] = l_currentGps.pitchrate;
    location["gear"] = l_currentActuator.gear;
    location["steeringWheelAngle"] = l_currentActuator.epsangle;
    location["acceleratorPedal"]= l_currentControllon.gasPedal;
    location["braking"]= l_currentControllon.brakePedal;
    location["blinkerStatus"] = 0;
    location["indicatorStatus"] = m_decisionbehavior.turnlights;



    jsonData["value"].append(location);
    jsonData["internalDelay"] = pingResust(m_serverIP);
    jsonData["cpeDelay"] = pingResust(m_cpeIP);
    jsonData["bStationDelay"] = pingResust(m_baseIP);
    
    std::unique_lock<std::mutex> lk_net(m_net_mutex);
    jsonData["networkUp"] = m_sendNet;
    jsonData["networkDown"] = m_recvNet;
    lk_net.unlock();

    return jsonData;
}

Json::Value redis_fusion::svframe(int p_frame)
{
    std::unique_lock<std::mutex> lk_object(m_currentObjectMutex);
    auto p_object = m_objects;
    lk_object.unlock();

    Json::Value jsonData;
    jsonData["type"];
    //组装value的消息 objectValue
            
    Json::Value location(Json::objectValue);
    location["timestampType"] = std::to_string(p_object.timestamp);
    location["timestamp"] = std::to_string(p_object.timestamp);
    location["role"] = "mvSimulation";




    int l_size = p_object.obs.size();
    location["source"] = m_taskStatus.fusionResult;
    for(int i = 0; i < l_size; i++)
    {   

        if(p_object.obs[i].classification == 7)
        {
            continue;
        }
        
        Json::Value objectArray;  
        // std::cout<<"objectArray:"<<objectArray<<std::endl;
        long l_time = ros::Time::now().toSec()*1000;
        objectArray["timestamp"] =  std::to_string(l_time);
        objectArray["globalTimeStamp"]= std::to_string(l_time);
        objectArray["id"] = p_object.obs[i].id;
        objectArray["name"]= p_object.obs[i].id;
        objectArray["length"] = p_object.obs[i].length;
        objectArray["width"] = p_object.obs[i].width;
        objectArray["height"] = p_object.obs[i].height;


        objectArray["longitude"] = p_object.obs[i].longtitude;//p_object.objs[i].xabs;  //相对位置信息
        objectArray["latitude"] = p_object.obs[i].latitude;//p_object.objs[i].yabs;    //相对位置信息
        objectArray["relative_x"] = -p_object.obs[i].relspeedx;    //相对位置信息
        objectArray["relative_y"] = p_object.obs[i].relspeedy;    //相对位置信息
        objectArray["relative_z"] = 0;    //相对位置信息
        if(p_object.obs[i].value == 5 || p_object.obs[i].value == 9 || p_object.obs[i].value == 8)
        {
            objectArray["carSource"] = 2;
        }else
        {
            objectArray["carSource"] = 1;
        }
        float l_angle = p_object.obs[i].azimuth * 180 / M_PI; // + m_currentGps.heading; 改为绝对角度
        objectArray["courseAngle"] = l_angle;//p_object.obs[i].azimuth * 180 / M_PI - m_currentGps.heading;;
        
        if(l_angle > 360)
        {
            objectArray["courseAngle"] = l_angle - 360;
        }
        std::cout<<i<<"   result:"<<objectArray["courseAngle"]<<"    fusion result:"<<p_object.obs[i].azimuth<<std::endl;
        objectArray["speed"] = sqrtf(p_object.obs[i].speeds * p_object.obs[i].speeds + p_object.obs[i].speedl * p_object.obs[i].speedl) * 3.6;   //相对速度信息
        objectArray["vehicleType"] = p_object.obs[i].classification;
#if 0
        int l_type = p_object.objs[i].type;
        if(l_type == 2){
            objectArray["vehicleType"] = 4;  //  xingren 
        }else if(l_type == 0){
            objectArray["vehicleType"] =1;   //car
        }else if(l_type == 3){
            objectArray["vehicleType"] = 13;  //cycle
        }else if(l_type == 4){
            objectArray["vehicleType"] =  99;
        }else if(l_type == 17 || l_type == 18 || l_type == 19){
            objectArray["vehicleType"] = l_type;
        }else{
            objectArray["vehicleType"] =  p_object.objs[i].type;
        }

#endif
        location["value"].append(objectArray);

    }
   return location;
}





void redis_fusion::svframeLog( common_msgs::sensorobjects p_object)
{
  
    Json::Value jsonData;
    jsonData["type"];
    //组装value的消息 objectValue
            
    Json::Value location(Json::objectValue);
    location["timestampType"] = std::to_string(p_object.timestamp);
    location["timestamp"] = std::to_string(p_object.timestamp);
    location["role"] = "mvSimulation";
    int l_size = p_object.obs.size();
    location["source"] = m_taskStatus.fusionResult;
    for(int i = 0; i < l_size; i++)
    {
        
        Json::Value objectArray;  
        // std::cout<<"objectArray:"<<objectArray<<std::endl;
        long l_time = ros::Time::now().toSec()*1000;
        objectArray["timestamp"] =  std::to_string(l_time);
        objectArray["globalTimeStamp"]= std::to_string(l_time);
        objectArray["id"] = p_object.obs[i].id;
        objectArray["name"]= p_object.obs[i].id;
        objectArray["length"] = p_object.obs[i].length;
        objectArray["width"] = p_object.obs[i].width;
        objectArray["height"] = p_object.obs[i].height;


        objectArray["longitude"] = p_object.obs[i].longtitude;//p_object.objs[i].xabs;  //相对位置信息
        objectArray["latitude"] = p_object.obs[i].latitude;//p_object.objs[i].yabs;    //相对位置信息
        objectArray["relative_x"] = -p_object.obs[i].relspeedx;    //相对位置信息
        objectArray["relative_y"] = p_object.obs[i].relspeedy;    //相对位置信息
        objectArray["relative_z"] = 0;    //相对位置信息
        objectArray["carSource"] = 0;//p_object.obs[i].classification;
      

        objectArray["courseAngle"] = p_object.obs[i].azimuth * 180 / M_PI + m_currentGps.heading;;
      
        objectArray["speed"] = sqrtf(p_object.obs[i].speeds * p_object.obs[i].speeds + p_object.obs[i].speedl * p_object.obs[i].speedl) * 3.6;   //相对速度信息
        objectArray["vehicleType"] = p_object.obs[i].classification;
        
        location["value"].append(objectArray);

    }
        static Json::FastWriter l_fastwriter ;
        std::string lineStr  = l_fastwriter.write(location);

}

void redis_fusion::SubCallback_taskstatus(const device_msgs::taskstatus::ConstPtr &msg)
{
    long l_time =ros::Time::now().toSec()*1000;
    std::string l_logstr = std::to_string(l_time) + ":  I am living.....";
    m_livinglog<<l_logstr<<std::endl;
    if(m_taskStatus.taskStatus != msg->taskStatus)
    {
        if(!msg->taskStatus)
        {
            m_taskStatus.taskStatus = msg->taskStatus;
        }
        if(msg->taskStatus)
        {
            ROS_INFO("recive task status");
            std::string l_id = getTaskID(msg->simResult);
            std::string logfile = m_logfile +m_deviceID + "_"+ l_id + "_fusion.log";
            InitLogger(logfile);
            logger= spdlog::get("console");
            m_taskStatus = *msg;
            //创建仿真车辆下发ros线程
            auto l_tmp = std::thread(&redis_fusion::fusionToCloudTH, this);
	        l_tmp.detach();

        }else
        {
            UninitLogger();
        }
       
        
    }
    
}

std::string redis_fusion::getTaskID(const std::string p_info)
{
    size_t firstpos = p_info.find('_');
    if(firstpos == std::string::npos)
    {
        return "";
    }
    size_t secondPos = p_info.find('_', firstpos+1);
    if(secondPos == std::string::npos)
    {
        return "";
    }  
    return p_info.substr(firstpos+1, secondPos-firstpos-1);
}



double redis_fusion::pingResust(const std::string &p_ip)
{
    std::stringstream command;
    command << "ping -c 1" << " " << p_ip;

    // 执行命令
    
    FILE* pipe = popen(command.str().c_str(), "r");
    if (!pipe) {
        return -1;
    }

    char buffer[128];
    std::string result = "";
    while(!feof(pipe)) {
        if(fgets(buffer, 128, pipe) != NULL)
            result += buffer;
    }
    pclose(pipe);
    //paser obtain data
    std::istringstream iss(result);
    std::string line;
    double avgLatency = -1;
    bool found = false;
    while(std::getline(iss, line)) {
        if(line.find("rtt min/avg/max/mdev") != std::string::npos) {
            // 提取平均时延
            size_t pos = line.find(" = ");
            if(pos != std::string::npos) {
                std::string latencyPart = line.substr(pos + 3);
                std::istringstream latencyStream(latencyPart);
                double min, avg, max, mdev;
                char slash;
                latencyStream >> min >> slash >> avg >> slash >> max >> slash >> mdev;
                avgLatency = avg;
                found = true;
            }
            break;
        }
    }

    if (!found) {
        std::cerr << "Failed to find average latency in ping output." << std::endl;
    }
    std::cout<<"avgLatency:"<<avgLatency<<std::endl;
    return avgLatency;
}

NetworkStats redis_fusion::readNetworkStats(const std::string& interface)
{
    NetworkStats stats;
    std::ifstream file("/proc/net/dev");
    std::string line;

    while (std::getline(file, line)) {
        if (line.find(interface) != std::string::npos) {
            std::istringstream iss(line);
            std::string iface;
            float l_empty;
            iss >> iface;
            unsigned long long l_value[9];
            for(int k =0; k < 9; k++)
            {
                iss>>l_value[k];
            }
            stats.rx_bytes = l_value[0];
            stats.tx_bytes = l_value[8];
            std::cout<<"iface:"<<iface<<"       "<<stats.rx_bytes<<"      "<<stats.tx_bytes<<std::endl;
            break;
        }
    }

    return stats;
}



void redis_fusion::bandwidthResult()
{
    while (1)
    {
        NetworkStats prev_stats = readNetworkStats(m_netNum);
        //usleep(100*1000);  // 等待指定的时间间隔
        sleep(1);
        NetworkStats curr_stats = readNetworkStats(m_netNum);

        unsigned long long rx_bytes_diff = curr_stats.rx_bytes - prev_stats.rx_bytes;
        unsigned long long tx_bytes_diff = curr_stats.tx_bytes - prev_stats.tx_bytes;
        std::cout << "Download : " << rx_bytes_diff << " bytes" << std::endl;
        std::cout << "Upload : " << tx_bytes_diff << " bytes" << std::endl;
        double interval = 1;
        // m_netStats.rx_bytes 
        int l_recv = static_cast<double>(rx_bytes_diff) / interval  / 1024.0 * 100;  // KB/s
        // m_netStats.tx_bytes 
        int l_send= static_cast<double>(tx_bytes_diff) / interval  / 1024.0 * 100;  // KB/s
        
        m_recvNet= l_recv / 100.0  ;
        m_sendNet = l_send / 100.0;

        std::cout << "Download Bandwidth: " << m_recvNet << " MB/s  0.1s" << std::endl;
        std::cout << "Upload Bandwidth: " << m_sendNet << " MB/s  0.1s"<< std::endl;
    }

}

void redis_fusion::SubCallback_gps(const common_msgs::sensorgps::ConstPtr &msg)
{

    std::unique_lock<std::mutex> lk(m_currentGpsMutex);
    m_currentGps= *msg;
    lk.unlock();

}


void redis_fusion::SubCallback_actuator(const common_msgs::actuator::ConstPtr &msg)
{  
    std::unique_lock<std::mutex> lk(m_currentActuatorMutex);
    m_currentActuator = *msg;
    lk.unlock();
}

void redis_fusion::SubCallback_vehiclefdb(const common_msgs::controllon::ConstPtr &msg)
{
    std::unique_lock<std::mutex> lk(m_currentVehicleMutex);
    m_currentControllon = (*msg);
    lk.unlock();
}


void redis_fusion::SubCallback_object(const common_msgs::sensorobjects::ConstPtr &msg)
{
    std::unique_lock<std::mutex> lk(m_currentObjectMutex);
    m_objects = (*msg);
    lk.unlock();


}


void redis_fusion::SubCallback_decisionbehavior(const common_msgs::decisionbehavior::ConstPtr &msg)
{
    std::unique_lock<std::mutex> lk(m_currentdecisionbehaviorMutex);
    m_decisionbehavior = *msg; 
    lk.unlock();
}